// device-list.ts
import { onPageShow } from '../../utils/tabbar.js';
import { getUserDevices } from '../../api/user';
import type { Device } from '../../api/types';
const dayjs = require('dayjs');
interface DeviceInfo {
  id: string;
  code: string;
  voltage: string;
  gpsTime: string;
  rentTime: string;
  warrantyTime: string;
  batteryLevel: number;
  isOnline: boolean;
  rawDevice?: Device; // 保存原始设备数据
}

Page({
  data: {
    searchValue: '',
    activeTab: 'all',
    totalCount: 0,
    onlineCount: 0,
    offlineCount: 0,
    deviceList: [] as DeviceInfo[],
    allDevices: [] as DeviceInfo[],
    navBarHeight: 0, // 导航栏高度
    searchSectionTop: 0, // 搜索栏top值
    tabsSectionMarginTop: 0, // 标签页margin-top值
  },

  onLoad() {
    this.calculateNavBarHeight();
    this.loadUserDevices();
  },

  onShow() {
    console.log('设备列表页面显示');
    // 使用工具函数更新自定义tabbar状态
    onPageShow(this);

    // 页面显示时刷新设备列表（可选）
    // this.loadUserDevices();
  },

  // 计算导航栏高度
  calculateNavBarHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const { statusBarHeight = 0 } = systemInfo;

    // 导航栏高度 = 状态栏高度 + 导航栏内容高度(一般是44px)
    const navBarHeight = statusBarHeight + 44 - 4;

    // 搜索栏top = 导航栏高度
    const searchSectionTop = navBarHeight;

    // 标签页margin-top = 导航栏高度 + 搜索栏高度(大约64px)
    const tabsSectionMarginTop = 54 + 5;

    this.setData({
      navBarHeight,
      searchSectionTop,
      tabsSectionMarginTop,
    });
  },

  // 加载用户设备列表
  async loadUserDevices() {
    try {
      wx.showLoading({
        title: '加载设备列表...',
        mask: true,
      });

      const response = await getUserDevices();
      console.log('获取用户设备列表:', response);

      if (response) {
        const deviceList = this.transformDeviceData(response);

        // 计算统计数据
        const onlineDevices = deviceList.filter((device) => device.isOnline);
        const offlineDevices = deviceList.filter((device) => !device.isOnline);

        this.setData({
          allDevices: deviceList,
          deviceList: deviceList,
          totalCount: deviceList.length,
          onlineCount: onlineDevices.length,
          offlineCount: offlineDevices.length,
        });
      }
    } catch (error) {
      console.error('加载设备列表失败:', error);
      wx.showToast({
        title: '加载失败，使用模拟数据',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 转换设备数据格式
  transformDeviceData(devices: Device[]): DeviceInfo[] {
    return devices.map((device) => {
      // 从设备属性中提取相关信息
      const properties = device.properties || {};

      // 使用PackVol字段（单位mV，需要转换为V）
      const packVolMv = properties.PackVol;
      const voltage = packVolMv ? `${(packVolMv / 1000).toFixed(1)}V` : '0.0V';

      // 实时电流 PackCur
      const packCurMa = properties.PackCur;
      const current = packCurMa ? `${(packCurMa / 1000).toFixed(1)}A` : '0.0A';

      // 功率 - 电压(V) × 电流(A) = 功率(W)
      const powerDisplay =
        packVolMv && packCurMa
          ? `${((packVolMv * packCurMa) / 1000000).toFixed(1)}W`
          : '0.0W';

      // 使用SOC字段作为电池电量
      const batteryLevel = properties.SOC || 0;

      // 格式化时间
      const rentTime = this.formatTimestamp(device.createdAt);

      const activeTime = this.formatTimestamp(
        device.activeTime || device.createdAt
      );

      return {
        id: device.id,
        code: device.sn,
        voltage: voltage,
        current: current,
        power: powerDisplay,
        gpsTime: properties.GPSTime || '未知',
        rentTime: rentTime,
        activeTime: activeTime,
        warrantyTime: '24小时', // 默认保修时间
        batteryLevel: Number(batteryLevel),
        isOnline: device.online,
        rawDevice: device, // 保存原始设备数据
      };
    });
  },

  // 格式化时间戳
  formatTimestamp(timestamp: number): string {
    if (!timestamp) return '未知';
    return dayjs(timestamp * 1000).format('YYYY-MM-DD');

    // 判断时间戳是秒级还是毫秒级
    const date =
      timestamp > 1000000000000
        ? new Date(timestamp) // 毫秒级时间戳
        : new Date(timestamp * 1000); // 秒级时间戳

    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${month}-${day} ${hours}:${minutes}`;
  },

  // 搜索输入变化 - 适配自定义搜索框
  onSearchChange(event: any) {
    const { value } = event.detail;
    this.setData({ searchValue: value });
    this.filterDevices();
  },

  // 点击搜索按钮
  onSearch() {
    this.filterDevices();
  },

  // 标签页切换
  onTabChange(event: any) {
    const { value } = event.detail;
    this.setData({ activeTab: value });
    this.filterDevices();
  },

  // 过滤设备列表
  filterDevices() {
    const { searchValue, activeTab, allDevices } = this.data;
    let filteredDevices = allDevices;

    // 根据搜索关键词过滤
    if (searchValue.trim()) {
      filteredDevices = filteredDevices.filter((device) =>
        device.code.toLowerCase().includes(searchValue.toLowerCase())
      );
    }

    // 根据标签页过滤
    switch (activeTab) {
      case 'online':
        filteredDevices = filteredDevices.filter((device) => device.isOnline);
        break;
      case 'offline':
        filteredDevices = filteredDevices.filter((device) => !device.isOnline);
        break;
      default:
        // 全部，不需要额外过滤
        break;
    }

    this.setData({ deviceList: filteredDevices });
  },

  // 定位按钮点击
  onLocationTap(event: any) {
    const { device } = event.currentTarget.dataset;
    const deviceSn = device.rawDevice?.sn || device.code;

    // 这里可以集成地图定位功能
    wx.showToast({
      title: `定位设备: ${deviceSn}`,
      icon: 'none',
      duration: 2000,
    });
    console.log('定位设备:', device);

    // TODO: 实现真实的设备定位功能
    // 可以跳转到地图页面或者调用定位API
  },

  // BMS按钮点击
  onBMSTap(event: any) {
    const { device } = event.currentTarget.dataset;
    const deviceSn = device.rawDevice?.sn || device.code;

    // 跳转到设备详情页面查看BMS信息
    wx.navigateTo({
      url: `/pages/tabbar/detail/detail?sn=${deviceSn}`,
    });
    console.log('查看BMS:', device);
  },

  // 轨迹按钮点击
  onTrackTap(event: any) {
    const { device } = event.currentTarget.dataset;
    const deviceSn = device.rawDevice?.sn || device.code;

    wx.showToast({
      title: `查看轨迹: ${deviceSn}`,
      icon: 'none',
      duration: 2000,
    });
    console.log('查看轨迹:', device);

    // TODO: 实现设备轨迹查看功能
    // 可以跳转到轨迹页面或者调用轨迹API
  },

  onDeviceTap(event: any) {
    const { device } = event.currentTarget.dataset;
    // 使用设备序列号作为参数，因为详情页面使用sn查询设备
    const sn = device.rawDevice?.sn || device.code;
    wx.navigateTo({
      url: `/pages/tabbar/detail/detail?sn=${sn}`,
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '设备列表',
      path: '/pages/device-list/device-list',
    };
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 重新加载设备数据
    this.loadUserDevices()
      .then(() => {
        wx.stopPullDownRefresh();
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500,
        });
      })
      .catch(() => {
        wx.stopPullDownRefresh();
        wx.showToast({
          title: '刷新失败',
          icon: 'none',
          duration: 1500,
        });
      });
  },
});
