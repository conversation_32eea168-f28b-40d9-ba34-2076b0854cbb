page {
  background-color: #f8f8f8;
}
.device-list-container {
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 搜索栏样式 */
.search-section {
  padding: 0 24rpx;
  height: 54px;
  background-color: #fff;
  position: fixed;
  z-index: 999;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05),
    0px 16px 24px 2px rgba(0, 0, 0, 0.04), 0px 8px 10px -5px rgba(0, 0, 0, 0.08);
}

.search-box {
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  margin-bottom: 16rpx;
}

.search-left-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
}

.search-input::placeholder {
  color: #f00;
}

.search-right-icon {
  width: 96rpx;
  height: 64rpx;
  margin-left: 16rpx;
}

/* 标签页样式 */
.tabs-section {
  background-color: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 自定义tabs样式 */
.tabs-section .t-tabs__content {
  padding: 0;
}

.tabs-section .t-tab-panel {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

.tabs-section .t-tabs__item--active .t-tab-panel {
  color: #1976d2;
  font-weight: 600;
}

.tabs-section .t-tabs__track {
  height: 4rpx;
  background-color: #1976d2;
  border-radius: 2rpx;
}

/* 设备列表 */
.device-list {
  flex: 1;
  padding: 16rpx 24rpx 16rpx 24rpx;
}

.device-item {
  margin-bottom: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx 0 24rpx 24rpx;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

  margin-right: 0;
  overflow: hidden;
}

.device-card {
  display: flex;
  align-items: center;
}

/* 状态标签 - 右上角 */
.status-tag {
  position: absolute;
  top: 0;
  right: 0;
  padding: 6rpx 12rpx;
  display: flex;
  align-items: center;
  border-radius: 0rpx 0rpx 0rpx 19rpx;
}

.status-tag.online {
  background-color: rgba(82, 196, 26, 0.1);
}

.status-tag.offline {
  background-color: #f3f3f3;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #52c41a;
  margin-right: 8rpx;
}
.status-dot.offline {
  background-color: #a6a6a6;
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
}

.status-tag.online .status-text {
  color: #52c41a;
}

.status-tag.offline .status-text {
  color: #5e5e5e;
}

/* 电池容器 */
.battery-container {
  margin-right: 30rpx;
  flex-shrink: 0;
  position: relative;
}
.battery-container .line {
  width: 120rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  position: absolute;
  top: 50%;
  left: calc(50% - 60rpx);
  transform: translateX(-50%);
  transform: translateY(-50%);
}

.battery-wrapper {
  width: 143rpx;
  height: 240rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义电池样式 */
.battery-body {
  width: 138rpx;
  height: 240rpx;
  border-radius: 19rpx;
  position: relative;
  overflow: hidden;
}
.battery-body.good {
  background: rgba(7, 217, 67, 0.1);
}
.battery-body.bad {
  background: rgba(255, 64, 0, 0.1);
}

.battery-head {
  position: absolute;
  top: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 32rpx;
  height: 8rpx;
  background-color: #ddd;
  border-radius: 4rpx 4rpx 0 0;
}

.battery-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: height 0.3s ease;
  border-radius: 19rpx;
}

.battery-wrapper.online .battery-body {
  border-color: #52c41a;
}

.battery-wrapper.online .battery-head {
  background: linear-gradient(180deg, #07d943 0%, #65f59c 100%);
}

.battery-wrapper.online .battery-fill {
  background: linear-gradient(180deg, #07d943 0%, #65f59c 100%);
  box-shadow: 0rpx 0rpx 53rpx 2rpx rgba(7, 217, 67, 0.3),
    0rpx 0rpx 11rpx 0rpx rgba(7, 217, 67, 0.3),
    inset 0rpx 0rpx 16rpx 10rpx rgba(0, 181, 51, 0.2);
}

.battery-wrapper.offline .battery-body {
  border-color: #ff4d4f;
}

.battery-wrapper.offline .battery-head {
  background-color: #ff4d4f;
}

.battery-wrapper.offline .battery-fill {
  background: linear-gradient(180deg, #ff4000 0%, #ffad43 100%);
  box-shadow: 0rpx 0rpx 53rpx 2rpx rgba(255, 64, 0, 0.3),
    0rpx 0rpx 11rpx 0rpx rgba(255, 64, 0, 0.3),
    inset 0rpx 0rpx 16rpx 10rpx rgba(255, 64, 0, 0.2);
}

.battery-level {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  margin-top: -14rpx; /* 向上偏移一些，让数字显示在填充区域内部 */
}

/* 设备信息 */
.device-info {
  flex: 1;
  min-width: 0;
  padding-right: 24rpx;
}

.device-code {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  margin-top: 8rpx;
}

.device-details {
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}
.detail-row.bg-gray {
  background: #f5f5f5;
  border-radius: 17rpx;
  padding: 5rpx 18rpx;
  margin-bottom: 16rpx;
  // max-width: 280rpx;
  display: inline-flex;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 32rpx;
  text-align: right;
  min-width: 120rpx;
}
.detail-text.bg-gray {
  margin-bottom: 0;
  margin-right: 0;
}

.detail-value {
  color: #1a1a1a;
  font-weight: 500;
  min-width: 100rpx;
  margin-right: 32rpx;
}
.detail-value-gray {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
}
.detail-text-gray {
  font-size: 28rpx;
  color: #666;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 24rpx;
  border-top: 1rpx solid #ececec;
  border-top-style: dashed;
  gap: 56rpx;
  margin-right: 24rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  min-width: 48rpx;
  width: 149rpx;
  height: 61rpx;
  background: #f2f3ff;
  border-radius: 40rpx;
}

.action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #1976d2;
  font-weight: 500;
  margin-bottom: 3rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
