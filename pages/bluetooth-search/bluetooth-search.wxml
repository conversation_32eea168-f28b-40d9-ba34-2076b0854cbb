<!--pages/bluetooth-search/bluetooth-search.wxml-->
<t-navbar title="蓝牙搜索" left-arrow />
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 搜索状态头部 -->
    <view class="search-header">
      <view class="search-icon {{isSearching ? 'rotating' : ''}}">
        <image src="/images/search.svg" class="search-image" />
      </view>
      <view class="search-status">
        <text class="status-text">{{statusText}}</text>
        <text class="status-desc">{{statusDesc}}</text>
      </view>
    </view>

    <!-- 设备列表 -->
    <view class="device-list" wx:if="{{devices.length > 0}}">
      <view class="section-title">发现的设备</view>
      <view class="device-item" wx:for="{{devices}}" wx:key="deviceId" bindtap="onDeviceSelect" data-device="{{item}}">
        <view class="device-icon">
          <image src="/images/bluetooth.svg" class="device-image" />
        </view>
        <view class="device-info">
          <view class="device-name">{{item.name || item.localName || '未知设备'}}</view>
          <view class="device-id">{{item.deviceId}}</view>
          <view class="device-rssi">信号强度: {{item.RSSI}}dBm</view>
        </view>
        <view class="device-arrow">
          <image src="/images/chevron-right.svg" class="arrow-icon" />
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!isSearching && devices.length === 0}}">
      <view class="empty-icon">
        <image src="/images/bluetooth.svg" class="empty-image" />
      </view>
      <view class="empty-text">未发现蓝牙设备</view>
      <view class="empty-desc">请确保设备已开启蓝牙并处于可发现状态</view>
      <button class="retry-button" bindtap="startSearch">重新搜索</button>
    </view>

    <!-- 搜索中状态 -->
    <view class="searching-state" wx:if="{{isSearching && devices.length === 0}}">
      <view class="loading-animation">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
      <view class="searching-text">正在搜索蓝牙设备...</view>
    </view>
  </view>
</scroll-view>