/* pages/bluetooth-search/bluetooth-search.less */

page {
  height: 100vh;
  background-color: #f8f9fa;
}

.scrollarea {
  flex: 1;
  padding-bottom: calc(env(safe-area-inset-bottom));
}

.container {
  padding: 32rpx;
  min-height: calc(100vh - 88rpx); /* 减去navbar高度 */
  display: flex;
  flex-direction: column;
}

/* 搜索状态头部 */
.search-header {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 40rpx 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  margin: 0 8rpx 32rpx 8rpx; /* 左右添加间距 */
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.25);
}

.search-icon {
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.search-icon.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.search-image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

.search-status {
  flex: 1;
}

.status-text {
  display: block;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.status-desc {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}

/* 设备列表 */
.device-list {
  // flex: 1;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  padding-left: 16rpx; /* 与设备项的间距保持一致 */
}

.device-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 28rpx 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin: 0 8rpx 12rpx 8rpx; /* 左右添加间距，减少底部间距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.device-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), transparent);
  transition: width 0.3s ease;
}

.device-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.device-item:active::before {
  width: 100%;
}

.device-icon {
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.device-image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.device-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.device-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 480rpx;
}

.device-id {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 4rpx;
  font-family: monospace;
}

.device-rssi {
  font-size: 24rpx;
  color: #666666;
}

.device-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.empty-image {
  width: 60rpx;
  height: 60rpx;
}

.empty-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999999;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 48rpx;
  padding: 20rpx 48rpx; /* 减少按钮高度 */
  font-size: 30rpx;
  font-weight: 600;
  min-width: 200rpx;
  line-height: 1.2;
}

.retry-button:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 搜索中状态 */
.searching-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.loading-animation {
  display: flex;
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  background: #667eea;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.searching-text {
  font-size: 32rpx;
  color: #667eea;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  padding: 32rpx 24rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.search-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.debug-btn {
  height: 72rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.debug-btn:active {
  background: #e9ecef;
}

.action-button {
  flex: 1;
  border: none;
  border-radius: 48rpx;
  padding: 24rpx 0; /* 减少按钮高度 */
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 16rpx rgba(102, 126, 234, 0.3);
}

.action-button.secondary {
  background: #ffffff;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.action-button:active {
  transform: scale(0.95);
}

.action-button.primary:active {
  box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.2);
}
