// pages/bluetooth-search/bluetooth-search.ts

interface BluetoothDevice {
  deviceId: string;
  name: string;
  localName: string;
  RSSI: number;
  advertisData: ArrayBuffer;
  advertisServiceUUIDs: string[];
  serviceData: object;
}

Page({
  data: {
    devices: [] as BluetoothDevice[],
    isSearching: true,
    statusText: '正在搜索蓝牙设备',
    statusDesc: '请稍候，正在搜索附近的蓝牙设备...',
    searchTimer: null as any,
  },

  onLoad() {
    // 延迟初始化，确保页面完全加载
    setTimeout(() => {
      this.checkBluetoothPermission();
    }, 300);
  },

  // 检查蓝牙权限
  checkBluetoothPermission() {
    // 直接尝试初始化蓝牙，小程序会自动处理权限请求
    this.initBluetooth();
  },

  onShow() {
    // 不在onShow时自动搜索，等待用户授权完成
  },

  onHide() {
    // 页面隐藏时停止搜索
    this.stopSearch();
  },

  onUnload() {
    // 页面卸载时清理资源
    this.cleanup();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.startSearch();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 初始化蓝牙适配器
  initBluetooth() {
    // 首先检查蓝牙是否可用
    wx.getBluetoothAdapterState({
      success: (res) => {
        console.log('蓝牙适配器状态:', res);
        if (res.available) {
          this.setData({
            statusText: '蓝牙已就绪',
            statusDesc: '点击开始搜索附近的蓝牙设备',
          });
          // 自动开始搜索
          this.startSearch();
        } else {
          this.setData({
            statusText: '蓝牙不可用',
            statusDesc: '请检查设备蓝牙是否开启',
          });
        }
      },
      fail: () => {
        // 适配器未初始化，尝试打开
        wx.openBluetoothAdapter({
          success: (res) => {
            console.log('蓝牙适配器初始化成功', res);
            this.setData({
              statusText: '蓝牙已就绪',
              statusDesc: '点击开始搜索附近的蓝牙设备',
            });
            // 自动开始搜索
            this.startSearch();
          },
          fail: (err) => {
            console.error('蓝牙适配器初始化失败', err);
            this.handleBluetoothError(err);
          },
        });
      },
    });
  },

  // 开始搜索蓝牙设备
  startSearch() {
    // if (this.data.isSearching) {
    //   return;
    // }

    this.setData({
      isSearching: true,
      devices: [],
      statusText: '正在搜索',
      statusDesc: '请稍候，正在搜索附近的蓝牙设备...',
    });

    // 先设置监听器，再开始搜索
    this.setupDeviceFoundListener();

    // 开始搜索设备
    wx.startBluetoothDevicesDiscovery({
      allowDuplicatesKey: true, // 允许重复上报，提高发现概率
      interval: 0,
      powerLevel: 'high', // 高功率搜索
      success: (res) => {
        console.log('开始搜索蓝牙设备成功', res);
        this.startSearchTimer();

        // 添加调试：检查是否真的开始搜索了
        setTimeout(() => {
          console.log('搜索状态检查，当前设备数量:', this.data.devices.length);
          if (this.data.devices.length === 0) {
            console.log('3秒后仍未发现设备，可能的原因：');
            console.log('1. 附近没有蓝牙设备');
            console.log('2. 设备过滤条件过于严格');
            console.log('3. 蓝牙权限问题');
            console.log('4. 设备监听器未正确设置');
          }
        }, 3000);

        // 再次检查6秒后的状态
        setTimeout(() => {
          if (this.data.devices.length === 0 && this.data.isSearching) {
            console.log('6秒后仍未发现设备，尝试重新启动搜索...');
            // 可以考虑重新启动搜索
          }
        }, 6000);
      },
      fail: (err) => {
        console.error('开始搜索失败', err);
        this.handleBluetoothError(err);
        this.setData({ isSearching: false });
      },
    });
  },

  // 停止搜索
  stopSearch() {
    if (!this.data.isSearching) {
      return;
    }

    wx.stopBluetoothDevicesDiscovery({
      success: (res) => {
        console.log('停止搜索成功', res);
      },
      fail: (err) => {
        console.error('停止搜索失败', err);
      },
    });

    this.clearSearchTimer();

    this.setData({
      isSearching: false,
      statusText: this.data.devices.length > 0 ? '搜索完成' : '未发现设备',
      statusDesc:
        this.data.devices.length > 0
          ? `发现 ${this.data.devices.length} 个设备`
          : '请检查设备是否开启蓝牙并重新搜索',
    });
  },

  // 设置设备发现监听
  setupDeviceFoundListener() {
    wx.onBluetoothDeviceFound((res) => {
      console.log('发现新设备原始数据:', res.devices);

      // 放宽过滤条件，显示所有有deviceId的设备
      const devices = res.devices.filter((device) => {
        // 只要有deviceId就显示，不再严格要求名称和信号强度
        const hasId = !!device.deviceId;
        const hasReasonableSignal = device.RSSI > -100; // 放宽信号强度要求

        console.log(`设备 ${device.deviceId}:`, {
          name: device.name,
          localName: device.localName,
          RSSI: device.RSSI,
          hasId,
          hasReasonableSignal,
        });

        return hasId && hasReasonableSignal;
      });

      console.log('过滤后的设备:', devices);

      if (devices.length > 0) {
        const currentDevices = this.data.devices;
        const newDevices = devices.filter(
          (newDevice) =>
            !currentDevices.some(
              (existDevice) => existDevice.deviceId === newDevice.deviceId
            )
        );

        if (newDevices.length > 0) {
          console.log('添加新设备到列表:', newDevices);
          this.setData({
            devices: [...currentDevices, ...newDevices].sort(
              (a, b) => (b.RSSI || -100) - (a.RSSI || -100)
            ),
            statusText: '发现设备',
            statusDesc: `已发现 ${
              currentDevices.length + newDevices.length
            } 个设备`,
          });
        }
      } else {
        console.log('没有符合条件的设备');
      }
    });
  },

  // 设置搜索定时器
  startSearchTimer() {
    this.clearSearchTimer();

    // 20秒后自动停止搜索
    this.data.searchTimer = setTimeout(() => {
      this.stopSearch();
    }, 20000);
  },

  // 清除搜索定时器
  clearSearchTimer() {
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
      this.setData({ searchTimer: null });
    }
  },

  // 选择设备
  onDeviceSelect(e: any) {
    const device = e.currentTarget.dataset.device;
    console.log('选择设备', device);

    // 停止搜索
    this.stopSearch();

    // 跳转到连接页面
    wx.navigateTo({
      url: `/pages/bluetooth-connect/bluetooth-connect?deviceId=${
        device.deviceId
      }&deviceName=${encodeURIComponent(
        device.name || device.localName || '未知设备'
      )}`,
    });
  },

  // 处理蓝牙错误
  handleBluetoothError(err: any) {
    let title = '蓝牙操作失败';
    let message = '';
    let solution = '';

    switch (err.errCode) {
      case 10000:
        title = '蓝牙未初始化';
        message = '蓝牙适配器未初始化';
        solution = '请重新进入页面或重启小程序';
        break;
      case 10001:
        title = '蓝牙不可用';
        message = '当前蓝牙适配器不可用，可能是蓝牙未开启';
        solution = '请打开手机蓝牙后重试';
        break;
      case 10002:
        title = '设备未找到';
        message = '没有找到指定设备';
        solution = '请确认设备在附近并重新搜索';
        break;
      case 10009:
        title = '系统不支持';
        message = 'Android 系统版本过低，不支持蓝牙 BLE';
        solution = '请升级系统版本或更换设备';
        break;
      default:
        title = '蓝牙错误';
        message = err.errMsg || '未知错误';
        solution = '请重试或重启小程序';
    }

    this.setData({
      statusText: title,
      statusDesc: solution,
    });

    // 显示详细错误信息
    wx.showModal({
      title: title,
      content: `${message}\n\n解决方案：${solution}`,
      showCancel: false,
      confirmText: '我知道了',
    });
  },

  // 清理资源
  cleanup() {
    this.stopSearch();
    this.clearSearchTimer();

    // 移除监听器
    wx.offBluetoothDeviceFound();

    // 关闭蓝牙适配器
    wx.closeBluetoothAdapter({
      success: (res) => {
        console.log('关闭蓝牙适配器成功', res);
      },
      fail: (err) => {
        console.error('关闭蓝牙适配器失败', err);
      },
    });
  },
});
