// pages/schedule-heat/schedule-heat.ts
import mqtt from '../../utils/mqtt.min.js';
const dayjs = require('dayjs');

interface HeatRecord {
  id: number;
  date: string;
  time: string;
  status: string;
  type: string;
}

Page({
  // 添加自定义属性
  client: null as any,
  currentDeviceSn: '' as string,

  // 蓝牙相关属性
  isBluetoothMode: false as boolean,
  bluetoothDeviceId: '' as string,
  bluetoothServiceId: '' as string,
  bluetoothCharacteristicId: '' as string,

  /**
   * 页面的初始数据
   */
  data: {
    showSchedulePopup: false,
    scheduleType: 'once', // once: 指定日期, weekly: 每周, monthly: 每月, daily: 每天
    selectedDateTime: new Date().getTime(), // 使用时间戳作为默认值
    date: new Date('2021-12-23').getTime(), // 支持时间戳传入

    // 指定选择区间起始值
    start: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    end: '2090-12-31 23:59:59',

    // 不同类型的时间选择
    monthlyDay: 1, // 每月的第几天 (1-31)
    monthlyHour: 7, // 每月的小时 (0-23)
    monthlyMinute: 0, // 每月的分钟 (0-59)
    monthlySecond: 0, // 每月的秒数 (0-59)
    weeklyDay: 1, // 每周的第几天 (1-7, 1=周一)
    weeklyHour: 7, // 每周的小时 (0-23)
    weeklyMinute: 0, // 每周的分钟 (0-59)
    weeklySecond: 0, // 每周的秒数 (0-59)
    dailyHour: 7, // 每天的小时 (0-23)
    dailyMinute: 0, // 每天的分钟 (0-59)
    dailySecond: 0,
    dailyTime: '07:00:00', // 每天的时间字符串格式

    // picker选项
    monthlyDayOptions: Array.from({ length: 31 }, (_, i) => ({
      label: `${i + 1}`,
      value: i + 1,
    })),
    weeklyDayOptions: [
      { label: '周一', value: 1 },
      { label: '周二', value: 2 },
      { label: '周三', value: 3 },
      { label: '周四', value: 4 },
      { label: '周五', value: 5 },
      { label: '周六', value: 6 },
      { label: '周日', value: 7 },
    ],
    hourOptions: Array.from({ length: 24 }, (_, i) => ({
      label: String(i).padStart(2, '0'),
      value: i,
    })),
    minuteOptions: Array.from({ length: 60 }, (_, i) => ({
      label: String(i).padStart(2, '0'),
      value: i,
    })),
    secondOptions: Array.from({ length: 60 }, (_, i) => ({
      label: String(i).padStart(2, '0'),
      value: i,
    })),

    // MQTT 配置
    host: 'mqtt.ricnsmart.com',
    mqttOptions: {
      username: 'new_energy_platform_web',
      password: 'jpr1AMN.rhx1txy*nvm',
      reconnectPeriod: 1000,
      connectTimeout: 30 * 1000,
    },
    // 模拟加热记录数据
    heatRecords: [
      {
        id: 1,
        date: '2025-06-23',
        time: '14:34:09',
        status: '定时：每周二',
        type: 'weekly',
      },
      {
        id: 2,
        date: '2025-06-23',
        time: '14:34:09',
        status: '定时：每月6号',
        type: 'monthly',
      },
      {
        id: 3,
        date: '2025-06-23',
        time: '14:34:09',
        status: '定时：每天',
        type: 'daily',
      },
      {
        id: 4,
        date: '2025-06-23',
        time: '14:34:09',
        status: '即时加热',
        type: 'immediate',
      },
      {
        id: 5,
        date: '2025-06-23',
        time: '14:34:09',
        status: '指定日期',
        type: 'once',
      },
    ] as HeatRecord[],

    // 显示模式标识
    isBluetooth: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    // 设置默认时间为当前时间
    this.setData({
      selectedDateTime: new Date().getTime(),
    });

    // 检查是否为蓝牙模式
    if (options.isBluetooth === 'true') {
      this.isBluetoothMode = true;
      this.bluetoothDeviceId = options.deviceId || '';
      this.setData({
        isBluetooth: true,
      });
      console.log('预约加热页面启用蓝牙模式，设备ID:', this.bluetoothDeviceId);

      // 获取蓝牙连接信息（这里需要从蓝牙连接页面获取）
      this.getBluetoothConnectionInfo();
    } else {
      // MQTT模式
      // 获取设备序列号（从首页或详情页传递过来）
      if (options.deviceSn) {
        this.currentDeviceSn = options.deviceSn;
      } else if (options.sn) {
        // 首页传递的参数名是 sn
        this.currentDeviceSn = options.sn;
      } else {
        // 尝试从缓存获取当前设备
        const selectedDevice = wx.getStorageSync('selectedDevice');
        if (selectedDevice && selectedDevice.sn) {
          this.currentDeviceSn = selectedDevice.sn;
        }
      }

      console.log('预约加热页面设备序列号:', this.currentDeviceSn);

      // 建立 MQTT 连接
      if (this.currentDeviceSn) {
        setTimeout(() => {
          this.connect();
        }, 1000);
      }
    }
  },

  // 获取蓝牙连接信息
  getBluetoothConnectionInfo() {
    // 这里需要获取当前蓝牙连接的服务ID和特征值ID
    // 由于微信小程序的限制，我们需要重新获取或从全局状态中获取
    if (this.bluetoothDeviceId) {
      wx.getBLEDeviceServices({
        deviceId: this.bluetoothDeviceId,
        success: (res) => {
          if (res.services.length > 0) {
            this.bluetoothServiceId = res.services[0].uuid;
            this.getBluetoothCharacteristics();
          }
        },
        fail: (err) => {
          console.error('获取蓝牙服务失败:', err);
        },
      });
    }
  },

  // 获取蓝牙特征值
  getBluetoothCharacteristics() {
    wx.getBLEDeviceCharacteristics({
      deviceId: this.bluetoothDeviceId,
      serviceId: this.bluetoothServiceId,
      success: (res) => {
        const writeCharacteristic = res.characteristics.find(
          (char) => char.properties.write
        );
        if (writeCharacteristic) {
          this.bluetoothCharacteristicId = writeCharacteristic.uuid;
          console.log('蓝牙写入特征值ID:', this.bluetoothCharacteristicId);
        }
      },
      fail: (err) => {
        console.error('获取蓝牙特征值失败:', err);
      },
    });
  },

  // 蓝牙发送数据
  sendBluetoothMessage(messageData: any) {
    if (
      !this.bluetoothDeviceId ||
      !this.bluetoothServiceId ||
      !this.bluetoothCharacteristicId
    ) {
      wx.showToast({
        title: '蓝牙连接信息不完整',
        icon: 'none',
      });
      return false;
    }

    try {
      // 将消息转换为字符串
      const messageString = JSON.stringify(messageData);
      console.log('发送蓝牙消息:', messageString);

      // 将字符串转换为ArrayBuffer
      const buffer = new ArrayBuffer(messageString.length);
      const uint8Array = new Uint8Array(buffer);
      for (let i = 0; i < messageString.length; i++) {
        uint8Array[i] = messageString.charCodeAt(i);
      }

      // 发送数据
      wx.writeBLECharacteristicValue({
        deviceId: this.bluetoothDeviceId,
        serviceId: this.bluetoothServiceId,
        characteristicId: this.bluetoothCharacteristicId,
        value: buffer,
        success: (res) => {
          console.log('蓝牙消息发送成功', res);
        },
        fail: (err) => {
          console.error('蓝牙消息发送失败', err);
          wx.showToast({
            title: '发送失败',
            icon: 'none',
          });
        },
      });

      return true;
    } catch (error) {
      console.error('蓝牙消息发送异常', error);
      wx.showToast({
        title: '发送异常',
        icon: 'none',
      });
      return false;
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    if (!this.isBluetoothMode) {
      console.log('预约加热页面隐藏，取消MQTT订阅');
      this.unsubscribe();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    if (!this.isBluetoothMode) {
      console.log('预约加热页面卸载，断开MQTT连接');
      this.disconnect();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新加热记录
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '预约加热 - 天储BMS',
      path: '/pages/schedule-heat/schedule-heat',
    };
  },

  /**
   * 返回按钮点击
   */
  onGoBack() {
    wx.navigateBack();
  },

  // MQTT 连接管理
  connect() {
    if (this.isBluetoothMode) {
      return; // 蓝牙模式不需要MQTT连接
    }

    // 如果已经有连接，先断开
    if (this.client) {
      this.disconnect();
    }

    try {
      const clientId = new Date().getTime();
      console.log('预约加热页面开始连接MQTT，clientId:', clientId);

      this.client = mqtt.connect(`wxs://${this.data.host}:8084/mqtt`, {
        ...this.data.mqttOptions,
        clientId,
      });

      this.client.on('connect', () => {
        console.log('预约加热页面MQTT 连接成功');
      });

      this.client.on('error', (error: any) => {
        console.error('预约加热页面MQTT连接错误:', error);
        this.client = null;
      });

      this.client.on('close', () => {
        console.log('预约加热页面MQTT连接关闭');
      });
    } catch (error) {
      console.error('预约加热页面MQTT连接初始化失败:', error);
      this.client = null;
    }
  },

  unsubscribe() {
    // 预约加热页面不需要订阅，只需要发布
  },

  disconnect() {
    if (this.client) {
      console.log('预约加热页面断开MQTT连接');
      try {
        this.client.end(true);
        this.client = null;
      } catch (error) {
        console.error('预约加热页面断开MQTT连接失败:', error);
        this.client = null;
      }
    }
  },

  // MQTT 发布消息
  publishMessage(deviceSn: string, command: string, params: any = {}) {
    if (!this.client || !this.client.connected) {
      console.error('MQTT未连接，无法发送命令');
      wx.showToast({
        title: 'MQTT未连接',
        icon: 'none',
      });
      return false;
    }

    // 根据文档构建发布主题: /sys/100/设备SN/thing/service/invoke
    const topic = `/sys/100/${deviceSn}/thing/service/invoke`;

    // 构建消息体
    const message = {
      [command]: params,
    };

    try {
      console.log('预约加热页面发布MQTT消息:', { topic, message });
      this.client.publish(topic, JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('预约加热页面发布MQTT消息失败:', error);
      wx.showToast({
        title: '发送命令失败',
        icon: 'none',
      });
      return false;
    }
  },

  /**
   * 立即加热
   */
  onImmediateHeat() {
    wx.showModal({
      title: '确认操作',
      content: '确定要立即开始加热吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '启动中...',
            mask: true,
          });

          let success = false;

          if (this.isBluetoothMode) {
            // 蓝牙模式 - 使用新的消息格式
            const bluetoothMessage = {
              dataType: '0x03',
              data: [
                {
                  PutWarm: [
                    {
                      Mode: 'immediate',
                      Immediate: [
                        {
                          Active: true,
                        },
                      ],
                    },
                  ],
                },
              ],
            };

            success = this.sendBluetoothMessage(bluetoothMessage);
          } else {
            // MQTT模式 - 使用原有的消息格式
            if (!this.currentDeviceSn) {
              wx.hideLoading();
              wx.showToast({
                title: '设备信息不完整',
                icon: 'none',
              });
              return;
            }

            const mqttMessage = {
              PutWarm: [
                {
                  Mode: 'immediate',
                  Immediate: {
                    Active: true,
                  },
                },
              ],
            };

            success = this.publishMessage(
              this.currentDeviceSn,
              'PutWarm',
              mqttMessage.PutWarm
            );
          }

          setTimeout(() => {
            wx.hideLoading();
            if (success) {
              wx.showToast({
                title: '立即加热命令已发送',
                icon: 'success',
              });

              // 添加新的记录到列表
              this.addHeatRecord({
                id: Date.now(),
                date: this.formatDate(new Date()),
                time: this.formatTime(new Date()),
                status: '即时加热',
                type: 'immediate',
              });
            } else {
              wx.showToast({
                title: '立即加热失败',
                icon: 'none',
              });
            }
          }, 1000);
        }
      },
    });
  },

  /**
   * 预约加热
   */
  onScheduleHeat() {
    this.setData({
      start: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      showSchedulePopup: true,
    });
  },

  /**
   * 预约弹窗显示状态变化
   */
  onSchedulePopupChange(event: any) {
    this.setData({
      showSchedulePopup: event.detail.visible,
    });
  },

  /**
   * 预约类型选择变化
   */
  onScheduleTypeChange(event: any) {
    console.log('选择类型变化:', event.detail.value);
    this.setData({
      scheduleType: event.detail.value,
    });
  },

  /**
   * 时间选择器变化
   */
  onDateTimeChange(event: any) {
    console.log('时间选择器变化:', event.detail);
    this.setData({
      selectedDateTime: event.detail.value,
    });
  },

  /**
   * 每月选择变化
   */
  onMonthlyChange(event: any) {
    const [dayIndex, hourIndex, minuteIndex, secondIndex] = event.detail.value;
    console.log('每月选择变化:', event.detail);
    this.setData({
      monthlyDay: dayIndex,
      monthlyHour: hourIndex,
      monthlyMinute: minuteIndex,
      monthlySecond: secondIndex,
    });
  },

  /**
   * 每月选择pick事件
   */
  onMonthlyPick(event: any) {
    console.log('每月pick:', event.detail);
  },

  /**
   * 每月选择confirm事件
   */
  onMonthlyConfirm(event: any) {
    console.log('每月confirm:', event.detail);
  },

  /**
   * 每周选择变化
   */
  onWeeklyChange(event: any) {
    const [dayIndex, hourIndex, minuteIndex, secondIndex] = event.detail.value;
    this.setData({
      weeklyDay: dayIndex,
      weeklyHour: hourIndex,
      weeklyMinute: minuteIndex,
      weeklySecond: secondIndex,
    });
  },

  /**
   * 每周选择pick事件
   */
  onWeeklyPick(event: any) {
    console.log('每周pick:', event.detail);
  },

  /**
   * 每周选择confirm事件
   */
  onWeeklyConfirm(event: any) {
    console.log('每周confirm:', event.detail);
  },

  /**
   * 每天选择变化
   */
  onDailyTimeChange(event: any) {
    console.log('每天时间变化:', event.detail);
    this.setData({
      dailyTime: event.detail.value,
    });
    // 同时更新小时和分钟字段，用于MQTT消息构建
    const [hour, minute, second] = event.detail.value.split(':').map(Number);
    this.setData({
      dailyHour: hour,
      dailyMinute: minute,
      dailySecond: second,
    });
  },

  /**
   * 每天选择pick事件
   */
  onDailyTimePick(event: any) {
    console.log('每天时间pick:', event.detail);
    this.setData({
      dailyTime: event.detail.value,
    });
    // 同时更新小时和分钟字段
    const [hour, minute, second] = event.detail.value.split(':').map(Number);
    this.setData({
      dailyHour: hour,
      dailyMinute: minute,
      dailySecond: second,
    });
  },

  /**
   * 每天选择confirm事件
   */
  onDailyTimeConfirm(event: any) {
    console.log('每天时间confirm:', event.detail);
    this.setData({
      dailyTime: event.detail.value,
    });
    // 同时更新小时和分钟字段
    const [hour, minute, second] = event.detail.value.split(':').map(Number);
    this.setData({
      dailyHour: hour,
      dailyMinute: minute,
      dailySecond: second,
    });
  },

  /**
   * 时间选择器选中
   */
  onDateTimePick(event: any) {
    console.log('时间选择器选中:', event.detail);
    this.setData({
      selectedDateTime: event.detail.value,
    });
  },

  /**
   * 时间选择器取消
   */
  onDateTimeCancel(event: any) {
    console.log('时间选择器取消:', event.detail);
  },

  /**
   * 时间选择器确认
   */
  onDateTimeConfirm(event: any) {
    console.log('时间选择器确认:', event.detail);
    this.setData({
      selectedDateTime: event.detail.value,
    });
  },

  /**
   * 取消预约
   */
  onCancelSchedule() {
    this.setData({
      showSchedulePopup: false,
    });
  },

  /**
   * 确认预约
   */
  onConfirmSchedule() {
    if (!this.currentDeviceSn) {
      wx.showToast({
        title: '设备信息不完整',
        icon: 'none',
      });
      return;
    }

    const { scheduleType } = this.data;

    wx.showLoading({
      title: '设置中...',
      mask: true,
    });

    // 构建不同类型的预约消息
    let timedConfig: any = {};
    let statusText = '';

    switch (scheduleType) {
      case 'once':
        // 指定日期加热
        const scheduledDate = new Date(this.data.selectedDateTime);
        const year = scheduledDate.getFullYear();
        const month = String(scheduledDate.getMonth() + 1).padStart(2, '0');
        const day = String(scheduledDate.getDate()).padStart(2, '0');
        const hours = String(scheduledDate.getHours()).padStart(2, '0');
        const minutes = String(scheduledDate.getMinutes()).padStart(2, '0');

        timedConfig = {
          Active: true,
          ScheduledDate: `${year}-${month}-${day}`,
          ScheduledTime: `${hours}:${minutes}:00`,
        };
        statusText = `指定日期: ${year}-${month}-${day} ${hours}:${minutes}`;
        break;

      case 'monthly':
        // 每月加热
        const monthlyTimeStr = `${String(this.data.monthlyHour).padStart(
          2,
          '0'
        )}:${String(this.data.monthlyMinute).padStart(2, '0')}:${String(
          this.data.monthlySecond
        ).padStart(2, '0')}`;
        timedConfig = {
          Active: true,
          Type: 'monthly',
          MonthlyDate: this.data.monthlyDay,
          MonthlyTime: monthlyTimeStr,
        };
        statusText = `定时：每月${this.data.monthlyDay}号 ${monthlyTimeStr}`;
        break;

      case 'weekly':
        // 每周加热
        const weeklyTimeStr = `${String(this.data.weeklyHour).padStart(
          2,
          '0'
        )}:${String(this.data.weeklyMinute).padStart(2, '0')}:${String(
          this.data.weeklySecond
        ).padStart(2, '0')}`;
        timedConfig = {
          Active: true,
          Type: 'weekly',
          WeeklyDate: this.data.weeklyDay,
          WeeklyTime: weeklyTimeStr,
        };
        const weekDayName =
          this.data.weeklyDayOptions[this.data.weeklyDay - 1].label;
        statusText = `定时：${weekDayName} ${weeklyTimeStr}`;
        break;

      case 'daily':
        // 每天加热
        const dailyTimeStr = `${String(this.data.dailyHour).padStart(
          2,
          '0'
        )}:${String(this.data.dailyMinute).padStart(2, '0')}:${String(
          this.data.dailySecond
        ).padStart(2, '0')}`;
        timedConfig = {
          Active: true,
          Type: 'daily',
          DailyTime: dailyTimeStr,
        };
        statusText = `定时：每天 ${dailyTimeStr}`;
        break;

      default:
        wx.hideLoading();
        wx.showToast({
          title: '未知的预约类型',
          icon: 'none',
        });
        return;
    }

    // 构建MQTT消息
    const mqttMessage = {
      PutWarm: [
        {
          Mode: scheduleType === 'once' ? 'scheduled' : 'timed',
          [scheduleType === 'once' ? 'Scheduled' : 'Timed']: [timedConfig],
        },
      ],
    };

    // 发送MQTT消息
    const success = this.publishMessage(
      this.currentDeviceSn,
      'PutWarm',
      mqttMessage.PutWarm
    );

    setTimeout(() => {
      wx.hideLoading();
      if (success) {
        wx.showToast({
          title: '预约设置成功',
          icon: 'success',
        });

        // 添加新的预约记录
        this.addHeatRecord({
          id: Date.now(),
          date: this.formatDate(new Date()),
          time: this.formatTime(new Date()),
          status: statusText,
          type: scheduleType,
        });

        this.setData({
          showSchedulePopup: false,
        });
      } else {
        wx.showToast({
          title: '预约设置失败',
          icon: 'none',
        });
      }
    }, 1000);
  },

  /**
   * 添加加热记录
   */
  addHeatRecord(record: HeatRecord) {
    const records = [record, ...this.data.heatRecords];
    this.setData({
      heatRecords: records,
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化时间
   */
  formatTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  },
});
