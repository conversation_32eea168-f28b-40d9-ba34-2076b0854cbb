// scan-device-info.ts
import { getDeviceBySn } from '../../api/device';
import { bindDevice } from '../../api/user';
import type { Device, BindDeviceRequest } from '../../api/types';
const app = getApp<IAppOption>();

Page({
  data: {
    safeAreaBottom: 0,

    // 设备信息
    deviceInfo: {
      serialNumber: '',
      firmwareVersion: '',
      hardwareVersion: '',
      name: '',
      online: false,
    },

    // 表单数据
    formData: {
      username: '',
      contact: '',
      carModel: '',
      plateNumber: '',
    },

    // 加载状态
    loading: false,
  },

  onLoad(options: any) {
    // 获取系统信息
    this.getSystemInfo();
    // 如果有扫码参数，可以在这里处理
    if (options.deviceId) {
      this.loadDeviceInfo(options.deviceId);
    }
  },

  // 获取系统信息
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      safeAreaBottom: systemInfo.safeArea
        ? systemInfo.windowHeight - systemInfo.safeArea.bottom
        : 0,
    });
  },

  // 加载设备信息
  async loadDeviceInfo(deviceId: string) {
    console.log('加载设备信息:', deviceId);

    this.setData({ loading: true });

    try {
      wx.showLoading({
        title: '加载设备信息...',
      });

      const response = await getDeviceBySn(deviceId);
      console.log('获取设备信息:', response);
      if (response) {
        const device: Device = response;
        const firmwareVersion = device?.properties?.FirmwareVersion
          ? `V ${device.properties.FirmwareVersion}`
          : '';
        const hardwareVersion = device?.properties?.HardwareVersion
          ? `V ${device.properties.HardwareVersion}`
          : '';
        this.setData({
          'deviceInfo.serialNumber': device.sn,
          'deviceInfo.firmwareVersion': firmwareVersion,
          'deviceInfo.hardwareVersion': hardwareVersion,
          'deviceInfo.name': device.name || `设备${device.sn}`,
          'deviceInfo.online': device.online,
          'formData.username': app.globalData.userInfo?.nickname || '',
          'formData.contact': app.globalData.userInfo?.mobile || '',
        });
      } else {
        throw new Error(response.message || '获取设备信息失败');
      }
    } catch (error: any) {
      console.error('加载设备信息失败:', error);
      wx.showToast({
        title: error.message || '获取设备信息失败',
        icon: 'none',
        duration: 2000,
      });

      // 设备不存在或获取失败时的默认处理
      this.setData({
        'deviceInfo.serialNumber': deviceId,
        'deviceInfo.firmwareVersion': '未知',
        'deviceInfo.hardwareVersion': '未知',
        'deviceInfo.name': `设备${deviceId}`,
        'deviceInfo.online': false,
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },
  // 返回上一页
  goBack() {
    wx.navigateBack({
      delta: 1,
    });
  },

  // 表单输入处理
  onUsernameInput(e: any) {
    this.setData({
      'formData.username': e.detail.value,
    });
  },

  onContactInput(e: any) {
    this.setData({
      'formData.contact': e.detail.value,
    });
  },

  onCarModelInput(e: any) {
    this.setData({
      'formData.carModel': e.detail.value,
    });
  },

  onPlateNumberInput(e: any) {
    this.setData({
      'formData.plateNumber': e.detail.value,
    });
  },

  // 验证表单
  validateForm(): boolean {
    const { formData } = this.data;

    if (!formData.username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none',
      });
      return false;
    }

    if (!formData.contact.trim()) {
      wx.showToast({
        title: '请输入联系方式',
        icon: 'none',
      });
      return false;
    }

    // 验证手机号格式（简单验证）
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.contact.trim())) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none',
      });
      return false;
    }

    if (!formData.carModel.trim()) {
      wx.showToast({
        title: '请输入车型',
        icon: 'none',
      });
      return false;
    }

    if (!formData.plateNumber.trim()) {
      wx.showToast({
        title: '请输入车牌号码',
        icon: 'none',
      });
      return false;
    }

    return true;
  },

  // 暂不绑定
  onSkipBind() {
    wx.showModal({
      title: '确认操作',
      content: '确定暂不绑定设备吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack({
            delta: 1,
          });
        }
      },
    });
  },

  // 确认绑定
  async onConfirmBind() {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    const { deviceInfo, formData } = this.data;

    if (this.data.loading) {
      return; // 防止重复提交
    }

    this.setData({ loading: true });

    try {
      wx.showLoading({
        title: '绑定中...',
        mask: true,
      });

      // 构建绑定请求数据
      const bindData: BindDeviceRequest = {
        sn: deviceInfo.serialNumber,
        real_name: formData.username.trim(),
        car_type: formData.carModel.trim(),
        car_no: formData.plateNumber.trim(),
        mobile: formData.contact.trim(),
      };

      const response = await bindDevice(bindData);
      console.log('设备绑定响应:', response);
      if (response.code === 200 || response.code === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '绑定成功',
          icon: 'success',
        });

        // 设置需要刷新设备列表的标记
        wx.setStorageSync('needRefreshDeviceList', true);
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/tabbar/index/index',
          });
        }, 1500);
      } else {
        throw new Error(response.message || '绑定失败');
      }
    } catch (error: any) {
      wx.hideLoading();
      console.error('设备绑定失败:', error);
      wx.showToast({
        title: error.message || '绑定失败，请重试',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '设备绑定 - 扫码设备信息',
      path: '/pages/scan-device-info/scan-device-info',
    };
  },
});
