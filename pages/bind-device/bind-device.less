/* scan-device-info.less */
page {
  height: 100vh;
  background: #ffffff;
}

/* 滚动区域 */
.scroll-view {
  width: 100%;
  height: calc(100vh - 128rpx); /* 减去导航栏高度和底部按钮高度 */
}

/* 设备信息卡片 */
.device-info-card {
  margin: 0;
  padding: 40rpx 48rpx;
  background: #ffffff;
  box-shadow: 0px 16rpx 20rpx -10rpx rgba(0, 0, 0, 0.08),
    0px 32rpx 48rpx 4rpx rgba(0, 0, 0, 0.04),
    0px 12rpx 60rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 0rpx 0rpx 40rpx 40rpx;
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32rpx;
}

.card-title {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 36rpx;
  line-height: 52rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx 0;
}

.info-label {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
}

.info-divider {
  flex: 1;
  height: 0;
  border-top: 2rpx dashed #e7e7e7;
}

.info-value {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 28rpx;
  line-height: 32rpx;
  color: rgba(0, 0, 0, 0.9);
  white-space: nowrap;
}

/* 表单区域 */
.form-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 32rpx;
}

.form-item {
  width: 100%;
  padding-left: 32rpx;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 32rpx 32rpx 32rpx 0;
  border-bottom: 1rpx solid #e7e7e7;
}

.form-label {
  width: 162rpx;
  flex-shrink: 0;
}

.label-text {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 32rpx;
  line-height: 48rpx;
  color: rgba(0, 0, 0, 0.9);
}

.form-content {
  flex: 1;
}

.form-input {
  width: 100%;
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 32rpx;
  line-height: 48rpx;
  color: #333;
}

.form-input::placeholder {
  color: rgba(0, 0, 0, 0.4);
}

/* 绑定人信息标题 */
.bound-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 32rpx 0;
}

.bound-title {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 36rpx;
  line-height: 52rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

/* 绑定人信息卡片 */
.bound-user-card {
  background: #f2f3ff;
  border-radius: 24rpx;
  padding: 32rpx 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 0 32rpx;
}

.user-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.user-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.user-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4rpx;
}

.user-name {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 32rpx;
  line-height: 48rpx;
  color: rgba(0, 0, 0, 0.9);
}

.contact-tag {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 16rpx;
  background: #f3f3f3;
  border-radius: 12rpx;
}

.contact-number {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 24rpx;
  line-height: 32rpx;
  color: rgba(0, 0, 0, 0.9);
}

/* 车辆信息 */
.vehicle-info {
  margin: 0 48rpx;
}

/* 底部占位 */
.bottom-placeholder {
  height: 180rpx;
}

/* 底部按钮区域 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: #ffffff;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.button-row {
  display: flex;
  gap: 32rpx;
}

.action-btn {
  flex: 1;
}

/* t-button 组件样式自定义 */
.skip-btn {
  --td-button-default-color: var(--primaryColor);
  --td-button-default-border-color: var(--primaryColor);
}

.bind-btn {
  --td-button-primary-bg-color: var(--primaryColor);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .card-title {
    font-size: 32rpx;
  }

  .info-label,
  .info-value {
    font-size: 26rpx;
  }

  .label-text,
  .form-input {
    font-size: 30rpx;
    color: #333;
  }
}
