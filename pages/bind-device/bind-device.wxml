<!--scan-device-info.wxml-->
<t-navbar title="绑定设备" left-arrow bind:go-back="goBack" />

<!-- 滚动内容区域 -->
<scroll-view class="scroll-view" scroll-y>
  <!-- 设备信息卡片 -->
  <view class="device-info-card">
    <view class="card-header">
      <text class="card-title">设备信息</text>
    </view>

    <view class="info-item">
      <text class="info-label">电池序列号</text>
      <view class="info-divider"></view>
      <text class="info-value">{{ deviceInfo.serialNumber }}</text>
    </view>

    <view class="info-item">
      <text class="info-label">固件版本</text>
      <view class="info-divider"></view>
      <text class="info-value">{{ deviceInfo.firmwareVersion }}</text>
    </view>

    <view class="info-item">
      <text class="info-label">硬件版本</text>
      <view class="info-divider"></view>
      <text class="info-value">{{ deviceInfo.hardwareVersion }}</text>
    </view>
  </view>

  <!-- 用户信息表单 - 仅在未绑定时显示 -->
  <view class="form-section">
    <view class="form-item">
      <view class="form-row">
        <view class="form-label">
          <text class="label-text">用户名</text>
        </view>
        <view class="form-content">
          <input class="form-input" placeholder="请输入用户名" value="{{ formData.username }}" bindinput="onUsernameInput"
            placeholder-style="color: #999;" maxlength="5" />
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-row">
        <view class="form-label">
          <text class="label-text">联系方式</text>
        </view>
        <view class="form-content">
          <input class="form-input" placeholder="请输入联系方式" value="{{ formData.contact }}" bindinput="onContactInput"
            type="number" placeholder-style="color: #999;" maxlength="11" />
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-row">
        <view class="form-label">
          <text class="label-text">车型</text>
        </view>
        <view class="form-content">
          <input class="form-input" placeholder="请输入车型" value="{{ formData.carModel }}" bindinput="onCarModelInput"
            placeholder-style="color: #999;" maxlength="16" />
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-row">
        <view class="form-label">
          <text class="label-text">车牌号码</text>
        </view>
        <view class="form-content">
          <input class="form-input" placeholder="请输入车牌号码" value="{{ formData.plateNumber }}"
            placeholder-style="color: #999;" bindinput="onPlateNumberInput" maxlength="10" />
        </view>
      </view>
    </view>
  </view>

  <!-- 底部占位，确保按钮不被遮挡 -->
  <view class="bottom-placeholder"></view>
</scroll-view>

<!-- 底部按钮区域 - 仅在未绑定时显示 -->
<view class="bottom-buttons" style="padding-bottom: {{ safeAreaBottom }}px;">
  <view class="button-row">
    <t-button variant="outline" theme="primary" shape="round" size="large" class="action-btn skip-btn"
      bind:tap="onSkipBind">
      暂不绑定
    </t-button>
    <t-button variant="base" theme="primary" shape="round" size="large" class="action-btn bind-btn"
      bind:tap="onConfirmBind">
      绑定
    </t-button>
  </view>
</view>