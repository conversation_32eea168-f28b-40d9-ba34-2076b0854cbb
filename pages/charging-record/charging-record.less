/* pages/charging-record/charging-record.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f6f7;
}

.page-container {
  flex: 1;
  overflow-y: hidden;
}

.content {
  padding: 25px;
  padding-bottom: 0;
}

.record-list {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.record-item {
  background: linear-gradient(90deg, #ffffff 0%, #ffffff 70%, #e3f9e9 100%);
  border-radius: 12px;
  padding: 14px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  min-height: 77px;
}

.record-content {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.time-icon {
  width: 20px;
  height: 20px;
  margin-top: 2px;

  image {
    width: 100%;
    height: 100%;
    tint-color: #001a57;
  }
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.start-time,
.end-time {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
  line-height: 24px;
}

.time-separator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  height: 30px;
  justify-content: center;
  margin: 2px 0;

  .separator-line {
    width: 0;
    height: 8px;
    border-left: 1px dashed #e7e7e7;
  }

  .separator-text {
    font-size: 12px;
    font-weight: 600;
    color: #a6a6a6;
    line-height: 12px;
  }
}

.record-divider {
  position: absolute;
  right: 64px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 77px;
  background-color: #e7e7e7;
}

.charge-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.charge-icon {
  width: 28px;
  height: 28px;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.08),
    0px 4px 5px 0px rgba(0, 0, 0, 0.06), 0px 1px 10px 0px rgba(0, 0, 0, 0.05);

  image {
    width: 12px;
    height: 14px;
    tint-color: linear-gradient(180deg, #00c94a 0%, #29ec53 100%);
  }
}

.charge-amount {
  font-size: 16px;
  font-weight: 600;
  color: #2ba471;
  line-height: 24px;
}

.safe-area-bottom {
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
}
