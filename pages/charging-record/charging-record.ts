Page({
  /**
   * 页面的初始数据
   */
  data: {
    recordList: [
      {
        id: 1,
        startTime: '2025-06-23  14:34:09',
        endTime: '2025-06-24  11:34:09',
        amount: '89Ah'
      },
      {
        id: 2,
        startTime: '2025-06-23  14:34:09',
        endTime: '2025-06-24  11:34:09',
        amount: '89Ah'
      },
      {
        id: 3,
        startTime: '2025-06-23  14:34:09',
        endTime: '2025-06-24  11:34:09',
        amount: '89Ah'
      },
      {
        id: 4,
        startTime: '2025-06-23  14:34:09',
        endTime: '2025-06-24  11:34:09',
        amount: '89Ah'
      },
      {
        id: 5,
        startTime: '2025-06-23  14:34:09',
        endTime: '2025-06-24  11:34:09',
        amount: '89Ah'
      },
      {
        id: 6,
        startTime: '2025-06-23  14:34:09',
        endTime: '2025-06-24  11:34:09',
        amount: '89Ah'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    console.log('充电记录页面加载');
    this.loadChargingRecords();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('充电记录页面显示');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadChargingRecords();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 可以在这里加载更多数据
    console.log('上拉触底');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  /**
   * 加载充电记录数据
   */
  loadChargingRecords() {
    // 这里可以调用API获取真实的充电记录数据
    // 现在使用模拟数据
    console.log('加载充电记录数据');
    
    // 模拟API请求
    setTimeout(() => {
      // 这里可以根据实际需要更新数据
      console.log('充电记录数据加载完成');
    }, 500);
  }
}); 