/**detail.ts**/
import { getDeviceBySn } from '../../../api/device';
import mqtt from '../../../utils/mqtt.min.js';

// 设备类型定义
interface Device {
  id: string;
  sn: string;
  online: boolean;
  uploadTime: number;
  properties: Record<string, any>;
}

interface BatteryInfo {
  level: number;
  status: string;
  serialNumber: string;
}

interface VoltageCell {
  number: string;
  voltage: string;
}

interface TemperatureCell {
  number: string;
  temperature: string;
}

interface BatteryStatusItem {
  label: string;
  value: boolean;
  display: string;
}

Page({
  // 添加自定义属性
  currentDeviceSn: '' as string,
  client: null as any,
  /**
   * 页面的初始数据
   */
  data: {
    isLoading: true, // 控制骨架屏显示
    showBatteryInfo: false, // 控制电池信息弹窗显示
    deviceInfo: {},

    batteryInfo: {
      level: 0,
      status: '加载中...',
      serialNumber: '',
    } as BatteryInfo,

    // 电池状态样式控制
    batteryStatus: 'normal', // 'normal' 或 'low'

    // 电压数据 - 初始为空，等待API数据
    voltageRows: [] as VoltageCell[][],

    // 温度数据 - 初始为空，等待API数据
    temperatureRows: [] as TemperatureCell[][],

    // 电池状态数据
    batteryStatusList: [] as BatteryStatusItem[],

    // 地图相关数据
    longitude: 116.397128,
    latitude: 39.916527,
    markers: [
      {
        id: 1,
        latitude: 39.916527,
        longitude: 116.397128,
        title: '设备位置',
        width: 30,
        height: 30,
      },
    ],

    host: 'mqtt.ricnsmart.com',
    subTopic: '/sys/+/+/thing/property/post',
    receivedMsg: '',
    mqttOptions: {
      username: 'new_energy_platform_web',
      password: 'jpr1AMN.rhx1txy*nvm',
      reconnectPeriod: 1000, // 1000毫秒，设置为 0 禁用自动重连，两次重新连接之间的间隔时间
      connectTimeout: 30 * 1000, // 30秒，连接超时时间
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    console.log('详情页面加载', options);

    // 从URL参数获取设备序列号
    const deviceSn = options.sn;
    this.currentDeviceSn = deviceSn;
    console.log('从URL参数获取设备序列号:', deviceSn);

    // 设置初始加载状态
    this.setData({ isLoading: true });

    // 获取位置
    this.getCurrentLocation();

    // 确保有设备序列号后再连接MQTT
    if (deviceSn) {
      // 连接MQTT
      setTimeout(() => {
        this.connect();
      }, 500); // 延迟连接，避免页面加载时的资源竞争

      // 加载设备详情
      this.fetchDeviceDetail(deviceSn);
    } else {
      // 尝试从缓存获取
      this.initPageData();
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('详情页面显示');
    // 每次显示时刷新数据
    this.refreshData();
  },

  connect() {
    // 如果已经有连接，先断开
    if (this.client) {
      this.disconnect();
    }

    try {
      const clientId = new Date().getTime();
      console.log('开始连接MQTT，clientId:', clientId);

      this.client = mqtt.connect(`wxs://${this.data.host}:8084/mqtt`, {
        ...this.data.mqttOptions,
        clientId,
      });

      this.client.on('connect', () => {
        console.log('MQTT 连接成功');
        this.subscribe();
      });

      this.client.on('message', (_topic: any, payload: any) => {
        try {
          console.log('MQTT 收到消息', payload + '');
          const deviceInfo = JSON.parse(payload + '');
          this.parseDeviceProperties(deviceInfo);
          console.log('设备信息:', deviceInfo);
        } catch (parseError) {
          console.error('解析MQTT消息失败:', parseError);
        }
      });

      this.client.on('error', (error: any) => {
        console.error('MQTT连接错误:', error);
        // 连接失败时清理客户端
        this.client = null;
      });

      this.client.on('reconnect', () => {
        console.log('MQTT重连中...');
      });

      this.client.on('offline', () => {
        console.log('MQTT离线');
      });

      this.client.on('close', () => {
        console.log('MQTT连接关闭');
      });
    } catch (error) {
      console.error('MQTT连接初始化失败:', error);
      this.client = null;
    }
  },

  subscribe() {
    if (this.client && this.client.connected && this.currentDeviceSn) {
      const topic = `/sys/+/${this.currentDeviceSn}/thing/property/post`;
      console.log('订阅MQTT主题:', topic);
      this.client.subscribe(topic);
    }
  },

  unsubscribe() {
    if (this.client && this.client.connected && this.currentDeviceSn) {
      const topic = `/sys/+/${this.currentDeviceSn}/thing/property/post`;
      console.log('取消订阅MQTT主题:', topic);
      this.client.unsubscribe(topic);
    }
  },

  disconnect() {
    if (this.client) {
      console.log('断开MQTT连接');
      try {
        // 先取消订阅
        this.unsubscribe();
        // 断开连接
        this.client.end(true); // 强制断开
        this.client = null;
      } catch (error) {
        console.error('断开MQTT连接失败:', error);
        this.client = null;
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('页面隐藏，取消MQTT订阅');
    this.unsubscribe();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('页面卸载，断开MQTT连接');
    this.disconnect();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '设备详情 - 天储BMS',
      path: '/pages/tabbar/detail/detail',
      imageUrl: '/images/share-detail.png',
    };
  },

  /**
   * 导航栏返回按钮点击事件
   */
  onGoBack() {
    wx.navigateBack();
  },

  /**
   * 显示设备列表
   */
  onShowDeviceList() {
    wx.navigateTo({
      url: '/pages/device-list/device-list',
    });
  },

  /**
   * 初始化页面数据（备用方法，主要通过URL参数获取设备信息）
   */
  initPageData() {
    // 从缓存获取当前选中的设备作为备选方案
    const selectedDevice = wx.getStorageSync('selectedDevice');
    if (selectedDevice && selectedDevice.sn) {
      console.log('从缓存获取设备信息:', selectedDevice.sn);
      this.currentDeviceSn = selectedDevice.sn;
      this.fetchDeviceDetail(selectedDevice.sn);
    } else {
      console.warn('未找到设备信息，请从设备列表进入');
      this.setData({ isLoading: false });
      wx.showToast({
        title: '未找到设备信息',
        icon: 'none',
        duration: 2000,
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp: number): string {
    if (!timestamp) return '未知';

    // 判断时间戳是秒级还是毫秒级
    const date =
      timestamp > 1000000000000
        ? new Date(timestamp) // 毫秒级时间戳
        : new Date(timestamp * 1000); // 秒级时间戳

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  },

  /**
   * 获取设备详情
   */
  async fetchDeviceDetail(deviceSn: string) {
    try {
      // 调用真实的设备详情API
      const result = await getDeviceBySn(deviceSn);
      console.log('API返回数据:', result);

      if (result) {
        const device = result;

        // 更新设备基本信息
        this.setData({
          isLoading: false,
          deviceInfo: device,
        });

        // 解析设备属性数据
        this.parseDeviceProperties(device.properties);
      } else {
        // API调用失败
        console.error('API调用失败或无数据:', result);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '获取设备信息失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('获取设备详情失败:', error);
      this.setData({ isLoading: false });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none',
        duration: 2000,
      });
    }
  },

  /**
   * 解析设备属性数据
   */
  parseDeviceProperties(properties: Record<string, any>) {
    // 如果 properties的SN和this.currentDeviceSn不匹配，则不处理
    if (properties.SN && properties.SN !== this.currentDeviceSn) {
      console.warn('设备序列号不匹配，忽略属性解析:', properties.SN);
      return;
    }
    try {
      // 解析电池基本信息
      const batteryInfo = {
        level: properties.SOC || 0, // 电量百分比
        status: this.getBatteryStatus(properties), // 电池状态
        serialNumber: properties.SN || '', // 设备序列号
      };

      // 解析单体电压数据 (CellVol1-CellVol8)
      const voltageRows = [];
      const cellCount = 8; // 根据API数据，有8个单体电压

      for (let i = 0; i < cellCount; i += 2) {
        const row = [];
        const cellIndex1 = i + 1;
        const cellIndex2 = i + 2;

        // 第一个电压
        if (properties[`CellVol${cellIndex1}`]) {
          row.push({
            number: `${cellIndex1}#`,
            voltage: properties[`CellVol${cellIndex1}`].toString(),
          });
        }

        // 第二个电压（如果存在）
        if (cellIndex2 <= cellCount && properties[`CellVol${cellIndex2}`]) {
          row.push({
            number: `${cellIndex2}#`,
            voltage: properties[`CellVol${cellIndex2}`].toString(),
          });
        }

        if (row.length > 0) {
          voltageRows.push(row);
        }
      }

      // 解析温度数据
      const temperatureRows = [];

      // 第一行：1# 和 接线柱-1
      temperatureRows.push([
        {
          number: '1#',
          temperature: (properties.BatteryT1 || 0).toString(),
        },
        {
          number: '接线柱-1',
          temperature: (properties.JpT1 || 0).toString(),
        },
      ]);

      // 第二行：2# 和 接线柱-2
      temperatureRows.push([
        {
          number: '2#',
          temperature: (properties.BatteryT2 || 0).toString(),
        },
        {
          number: '接线柱-2',
          temperature: (properties.JpT2 || 0).toString(),
        },
      ]);

      // 第三行：3# 和 环境温度
      temperatureRows.push([
        {
          number: '3#',
          temperature: (properties.BatteryT3 || 0).toString(),
        },
        {
          number: '环境温度',
          temperature: (properties.AmbientT || 0).toString(),
        },
      ]);

      // 第四行：4#
      temperatureRows.push([
        {
          number: '4#',
          temperature: (properties.BatteryT4 || 0).toString(),
        },
      ]);

      // 解析电池状态数据
      const batteryStatusList: BatteryStatusItem[] = [
        {
          label: '总体过压',
          value: properties.PackOVing || false,
          display: properties.PackOVing ? '是' : '否',
        },
        {
          label: '总体欠压',
          value: properties.PackUVWarning || false,
          display: properties.PackUVWarning ? '是' : '否',
        },
        {
          label: '单体过压',
          value: properties.CellOVWarning || false,
          display: properties.CellOVWarning ? '是' : '否',
        },
        {
          label: '单体欠压',
          value: properties.CellUVWarning || false,
          display: properties.CellUVWarning ? '是' : '否',
        },
        {
          label: '电芯 充电高温',
          value: properties.McuOTCWarning || false,
          display: properties.McuOTCWarning ? '是' : '否',
        },
        {
          label: '电芯 充电低温',
          value: properties.McuUTCWarning || false,
          display: properties.McuUTCWarning ? '是' : '否',
        },
        {
          label: '电芯 放电高温',
          value: properties.McuOTDWarning || false,
          display: properties.McuOTDWarning ? '是' : '否',
        },
        {
          label: '电芯 放电低温',
          value: properties.McuUTDWarning || false,
          display: properties.McuUTDWarning ? '是' : '否',
        },
        {
          label: '接线柱 充电高温',
          value: properties.JpOTC || false,
          display: properties.JpOTC ? '是' : '否',
        },
        {
          label: '接线柱 放电高温',
          value: properties.JpOTD || false,
          display: properties.JpOTD ? '是' : '否',
        },
        {
          label: '充电状态',
          value: properties.ChargeStatus || false,
          display: properties.ChargeStatus ? '是' : '否',
        },
        {
          label: '放电状态',
          value: properties.DischargeStatus || false,
          display: properties.DischargeStatus ? '是' : '否',
        },
        {
          label: '待机状态',
          value: properties.DIStatus || false,
          display: properties.DIStatus ? '是' : '否',
        },
        {
          label: '继电器 开启',
          value: properties.CtrlStatus || false,
          display: properties.CtrlStatus ? '是' : '否',
        },
        {
          label: '加热 关闭',
          value: properties.WarmStatus || false,
          display: properties.WarmStatus ? '是' : '否',
        },
        {
          label: '短路',
          value: properties.SC || false,
          display: properties.SC ? '是' : '否',
        },
        {
          label: '低SOC',
          value: properties.lowSOC || false,
          display: properties.lowSOC ? '是' : '否',
        },
      ];

      // 计算电池状态样式
      const batteryStatus = batteryInfo.level <= 20 ? 'low' : 'normal';

      this.setData({
        batteryInfo,
        batteryStatus,
        voltageRows,
        temperatureRows,
        batteryStatusList,
      });

      console.log('设备属性解析完成:', {
        batteryInfo,
        voltageRows,
        temperatureRows,
      });
    } catch (error) {
      console.error('解析设备属性失败:', error);
      // 解析失败时保持原有数据
    }
  },

  /**
   * 获取电池状态描述
   */
  getBatteryStatus(properties: Record<string, any>): string {
    if (properties.ChargeStatus) {
      return '充电中';
    } else if (properties.DischargeStatus) {
      return '放电中';
    } else if (properties.lowSOC) {
      return '低电量';
    } else if (properties.lowSOCWarning) {
      return '低电量警告';
    } else if (!properties.CtrlStatus) {
      return '离线';
    } else {
      return '正常';
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    // 获取当前设备信息并重新加载
    const currentDeviceSn = this.currentDeviceSn;

    if (currentDeviceSn) {
      // 如果有当前设备序列号，直接刷新
      console.log('刷新设备数据:', currentDeviceSn);
      // 只在首次加载时显示骨架屏
      if (this.data.isLoading) {
        this.fetchDeviceDetail(currentDeviceSn);
      } else {
        // 非首次加载显示loading提示
        wx.showLoading({ title: '刷新中...' });
        this.fetchDeviceDetail(currentDeviceSn).finally(() => {
          wx.hideLoading();
        });
      }
    } else {
      // 否则尝试从缓存获取
      const selectedDevice = wx.getStorageSync('selectedDevice');
      if (selectedDevice && selectedDevice.sn) {
        this.currentDeviceSn = selectedDevice.sn;
        this.fetchDeviceDetail(selectedDevice.sn);
      } else {
        // 没有设备信息，隐藏loading
        this.setData({ isLoading: false });
      }
    }
  },

  /**
   * 显示更多信息
   */
  onShowMoreInfo() {
    this.setData({
      showBatteryInfo: true,
    });
  },

  /**
   * 关闭电池信息弹窗
   */
  onCloseBatteryInfo() {
    this.setData({
      showBatteryInfo: false,
    });
  },

  /**
   * 弹窗显示状态变化
   */
  onBatteryInfoVisibleChange(event: any) {
    this.setData({
      showBatteryInfo: event.detail.visible,
    });
  },

  // 解绑设备
  onUnbindDevice() {
    wx.showModal({
      title: '确认解绑',
      content: '确定要解绑当前设备吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '解绑成功',
            icon: 'success',
          });
          // TODO: 实现解绑逻辑
        }
      },
    });
  },

  onRecord() {
    wx.navigateTo({
      url:
        '/pages/charging-record/charging-record?deviceId=' +
        this.currentDeviceSn,
    });
  },

  // 地图点击事件
  onMapTap() {
    wx.openLocation({
      latitude: this.data.latitude,
      longitude: this.data.longitude,
      name: '设备位置',
      address: '设备详细地址',
    });
  },

  // 获取当前位置
  getCurrentLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取位置成功:', res);
        this.setData({
          longitude: res.longitude,
          latitude: res.latitude,
          'markers[0].longitude': res.longitude,
          'markers[0].latitude': res.latitude,
        });
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        wx.showToast({
          title: '获取位置失败',
          icon: 'none',
        });
      },
    });
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '设备详情 - 天储BMS',
      imageUrl: '/images/share-detail.png',
    };
  },

  // MQTT 发布消息
  publishMessage(deviceSn: string, command: string, params: any = {}) {
    if (!this.client || !this.client.connected) {
      console.error('MQTT未连接，无法发送命令');
      wx.showToast({
        title: 'MQTT未连接',
        icon: 'none',
      });
      return false;
    }

    // 根据文档构建发布主题: /sys/100/设备SN/thing/service/invoke
    const topic = `/sys/100/${deviceSn}/thing/service/invoke`;

    // 构建消息体
    const message = {
      [command]: params,
    };

    try {
      console.log('详情页发布MQTT消息:', { topic, message });
      this.client.publish(topic, JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('详情页发布MQTT消息失败:', error);
      wx.showToast({
        title: '发送命令失败',
        icon: 'none',
      });
      return false;
    }
  },

  // 执行设备操作
  performDeviceAction(action: string) {
    if (!this.currentDeviceSn) {
      wx.showToast({
        title: '设备信息不完整',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '执行中...',
      mask: true,
    });

    let success = false;

    try {
      switch (action) {
        case '强制启动':
          success = this.publishMessage(this.currentDeviceSn, 'PutDI', 'open');
          break;
        case '远程开机':
          // 发送 PutCtrl 命令，参数为 "open"
          success = this.publishMessage(
            this.currentDeviceSn,
            'PutCtrl',
            'open'
          );
          break;

        case '远程关机':
          // 发送 PutCtrl 命令，参数为 "close"
          success = this.publishMessage(
            this.currentDeviceSn,
            'PutCtrl',
            'close'
          );
          break;

        case '立即加热':
          // 发送 PutWarm 命令，立即加热模式
          success = this.publishMessage(this.currentDeviceSn, 'PutWarm', {
            Mode: 'immediate',
          });
          break;

        default:
          console.warn('未知的设备操作:', action);
          success = false;
      }

      setTimeout(() => {
        wx.hideLoading();
        if (success) {
          wx.showToast({
            title: `${action}命令已发送`,
            icon: 'none',
          });
        } else {
          wx.showToast({
            title: `${action}失败`,
            icon: 'none',
          });
        }
      }, 1000);
    } catch (error) {
      console.error('执行设备操作失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: `${action}失败`,
        icon: 'none',
      });
    }
  },

  // 添加缺失的操作按钮事件处理
  onForceStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要强制启动设备吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('强制启动');
        }
      },
    });
  },

  onScheduleHeat() {
    wx.navigateTo({
      url:
        '/pages/schedule-heat/schedule-heat?deviceSn=' + this.currentDeviceSn,
    });
  },

  onRemoteStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程开机吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('远程开机');
        }
      },
    });
  },

  onRemoteShutdown() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程关机吗？此操作可能会影响设备正常运行。',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('远程关机');
        }
      },
    });
  },
});
