<!--pages/tabbar/detail/detail.wxml-->
<t-navbar title="{{'设备'}}" left-arrow>
</t-navbar>

<scroll-view class="page-container">
  <!-- 骨架屏 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-content">
      <!-- 骨架屏 - 电池状态卡片 -->
      <view class="skeleton-battery-card">
        <view class="skeleton-battery-visual"></view>
        <view class="skeleton-serial-info">
          <view class="skeleton-line skeleton-line-short"></view>
          <view class="skeleton-line skeleton-line-medium"></view>
        </view>
        <view class="skeleton-bottom-actions">
          <view class="skeleton-action-button"></view>
          <view class="skeleton-action-button"></view>
        </view>
      </view>

      <view class="skeleton-bottom-content">
        <!-- 骨架屏 - 单体电压卡片 -->
        <view class="skeleton-info-card">
          <view class="skeleton-card-header">
            <view class="skeleton-line skeleton-line-medium"></view>
          </view>
          <view class="skeleton-grid">
            <view class="skeleton-row">
              <view class="skeleton-cell"></view>
              <view class="skeleton-cell"></view>
            </view>
            <view class="skeleton-row">
              <view class="skeleton-cell"></view>
              <view class="skeleton-cell"></view>
            </view>
          </view>
        </view>

        <!-- 骨架屏 - 温度信息卡片 -->
        <view class="skeleton-info-card">
          <view class="skeleton-card-header">
            <view class="skeleton-line skeleton-line-medium"></view>
          </view>
          <view class="skeleton-grid">
            <view class="skeleton-row">
              <view class="skeleton-cell"></view>
              <view class="skeleton-cell"></view>
            </view>
            <view class="skeleton-row">
              <view class="skeleton-cell"></view>
              <view class="skeleton-cell"></view>
            </view>
          </view>
        </view>

        <!-- 骨架屏 - 地图 -->
        <view class="skeleton-map"></view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view wx:else class="main-content">

    <!-- 电池状态卡片 -->
    <view class="battery-status-card">
      <!-- 电池可视化 -->
      <view class="battery-container">
        <view class="battery-visual battery-visual-{{batteryStatus}}">
          <view class="battery-bars battery-bars-{{batteryStatus}}">
            <!-- 根据电池状态显示不同数量的电池条 -->
            <view class="battery-bar battery-bar-{{batteryStatus}}" wx:for="{{batteryStatus === 'low' ? 7 : 50}}"
              wx:key="index"></view>
          </view>
          <view class="battery-status">
            <text class="battery-percent battery-percent-{{batteryStatus}}">{{batteryInfo.level}}%</text>
            <view class="status-badge status-badge-{{batteryStatus}}">
              <image class="charging-icon" src="/images/lightning-gray.svg" mode="aspectFit"></image>
              <text class="status-text status-text-{{batteryStatus}}">{{batteryInfo.status}}</text>
            </view>
          </view>
        </view>
        <view class="battery-indicator battery-indicator-{{batteryStatus}}"></view>
      </view>

      <!-- 电池序列号信息 -->
      <view class="serial-info">
        <text class="serial-label">电池序列号</text>
        <text class="serial-number">{{batteryInfo.serialNumber}}</text>
        <text class="more-link" bindtap="onShowMoreInfo">更多</text>
      </view>

      <!-- 底部操作按钮 -->
      <view class="bottom-actions">
        <view class="bottom-action-button" bindtap="onUnbindDevice">
          <view class="bottom-button-icon">
            <image src="/images/unbind.svg" mode="aspectFit" />
          </view>
          <text class="bottom-button-text">解绑电池</text>
        </view>
        <view class="bottom-action-line" />
        <view class="bottom-action-button" bindtap="onRecord">
          <view class="bottom-button-icon">
            <image src="/images/recharging.svg" mode="aspectFit" />
          </view>
          <text class="bottom-button-text">充电记录</text>
        </view>
      </view>
    </view>

    <view class="bottom-main-content">
      <!-- 单体电压信息卡片 -->
      <view class="voltage-info-card">
        <!-- 卡片标题 -->
        <view class="card-header">
          <view class="title-line dashed"></view>
          <view class="title-content">
            <image class="title-icon" src="/images/voltmeter.svg" mode="aspectFit"></image>
            <text class="title-text">单体电压信息</text>
          </view>
          <view class="title-line dashed"></view>
        </view>

        <!-- 电压单元网格 -->
        <view class="voltage-grid">
          <view class="voltage-row" wx:for="{{voltageRows}}" wx:key="index" wx:for-index="rowIndex">
            <view class="voltage-cell" wx:for="{{item}}" wx:key="index" wx:for-item="cell">
              <view class="cell-header">
                <text class="cell-number">{{cell.number}}</text>
              </view>
              <view class="cell-divider"></view>
              <view class="cell-value">
                <text class="value-number">{{cell.voltage}}</text>
                <text class="value-unit">mV</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 温度信息卡片 -->
      <view class="temperature-info-card">
        <!-- 卡片标题 -->
        <view class="card-header">
          <view class="title-line dashed"></view>
          <view class="title-content">
            <image class="title-icon temperature" src="/images/temperature.svg" mode="aspectFit"></image>
            <text class="title-text">温度信息</text>
          </view>
          <view class="title-line dashed"></view>
        </view>

        <!-- 温度单元网格 -->
        <view class="temperature-grid">
          <view class="temperature-row" wx:for="{{temperatureRows}}" wx:key="index" wx:for-index="rowIndex">
            <view class="temperature-cell" wx:for="{{item}}" wx:key="index" wx:for-item="temp">
              <view class="cell-header temperature">
                <text class="cell-number">{{temp.number}}</text>
              </view>
              <view class="cell-divider"></view>
              <view class="cell-value">
                <text class="value-number">{{temp.temperature}}</text>
                <text class="value-unit">℃</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 电池状态信息卡片 -->
      <view class="battery-status-info-card">
        <!-- 卡片标题 -->
        <view class="card-header">
          <view class="title-line dashed"></view>
          <view class="title-content">
            <image class="title-icon" src="/images/bat.png" mode="aspectFit"></image>
            <text class="title-text">电池状态</text>
          </view>
          <view class="title-line dashed"></view>
        </view>

        <!-- 电池状态列表 -->
        <view class="battery-status-grid">
          <view class="battery-status-item" wx:for="{{batteryStatusList}}" wx:key="index">
            <view class="status-label">{{item.label}}</view>
            <view class="status-divider"></view>
            <view class="status-value" data-active="{{item.value}}">{{item.display}}</view>
          </view>
        </view>
      </view>

      <!-- 地图区域 -->
      <view class="map-container">
        <map class="device-map" longitude="{{longitude}}" latitude="{{latitude}}" scale="16" show-location="{{true}}"
          markers="{{markers}}" bindtap="onMapTap">
          <view class="map-overlay">
            <view class="location-icon">
              <image src="/images/full.svg" mode="aspectFit"></image>
            </view>
          </view>
        </map>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons">
      <view class="action-button" bindtap="onForceStart">
        <view class="button-icon">
          <image src="/images/start.svg" mode="aspectFit" tint-color="white" />
        </view>
        <text class="button-text">强制启动</text>
      </view>

      <view class="action-button" bindtap="onScheduleHeat">
        <view class="button-icon">
          <image src="/images/heating.svg" mode="aspectFit" />
        </view>
        <text class="button-text">预约加热</text>
      </view>

      <view class="action-button" bindtap="onRemoteStart">
        <view class="button-icon">
          <image src="/images/open.svg" mode="aspectFit" />
        </view>
        <text class="button-text">远程开机</text>
      </view>

      <view class="action-button" bindtap="onRemoteShutdown">
        <view class="button-icon">
          <image src="/images/shutdown.svg" mode="aspectFit" />
        </view>
        <text class="button-text">远程关机</text>
      </view>
    </view>
  </view>
  <view class="" style="height: calc(100rpx + env(safe-area-inset-bottom))"></view>

</scroll-view>

<!-- 电池信息弹窗 -->
<t-popup visible="{{showBatteryInfo}}" placement="bottom" bind:visible-change="onBatteryInfoVisibleChange">
  <view class="battery-info-popup">
    <!-- 弹窗标题 -->
    <view class="popup-header">
      <text class="popup-title">电池信息</text>
    </view>

    <!-- 信息列表 -->
    <view class="popup-content">
      <view class="info-item">
        <text class="info-label">电池序列号</text>
        <view class="info-divider"></view>
        <text class="info-value">{{batteryInfo.serialNumber}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">固件版本</text>
        <view class="info-divider"></view>
        <text class="info-value">V {{deviceInfo.properties.FirmwareVersion}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">硬件版本</text>
        <view class="info-divider"></view>
        <text class="info-value">V {{deviceInfo.properties.HardwareVersion}}</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-footer">
      <t-button variant="text" size="large" bind:tap="onCloseBatteryInfo" class="return-btn">
        返回
      </t-button>
    </view>
  </view>
</t-popup>