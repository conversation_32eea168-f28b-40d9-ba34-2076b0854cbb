/**index.less**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

/* Loading 状态容器 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding-bottom: calc(150rpx + env(safe-area-inset-bottom)); /* 适配tabbar */
}

.loading-content {
  flex: 1;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 骨架屏样式 */
.skeleton-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0px 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.skeleton-header {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
}

.skeleton-battery {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 24rpx 0;
}

.skeleton-battery-visual {
  width: 200rpx;
  height: 120rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-battery-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.skeleton-params {
  display: flex;
  justify-content: space-around;
  gap: 24rpx;
}

.skeleton-param {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.skeleton-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: space-between;
}

.skeleton-button {
  width: 160rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-map {
  height: 400rpx;
  background-color: #f5f5f5;
  border-radius: 24rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* 骨架屏线条样式 */
.skeleton-line {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-line-mini {
  height: 24rpx;
  width: 60rpx;
}

.skeleton-line-short {
  height: 32rpx;
  width: 120rpx;
}

.skeleton-line-medium {
  height: 28rpx;
  width: 200rpx;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-color: #f5f5f5;
  }
  50% {
    background-color: #e8e8e8;
  }
  100% {
    background-color: #f5f5f5;
  }
}

/* 无设备状态容器 */
.no-device-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  padding-bottom: calc(150rpx + env(safe-area-inset-bottom)); /* 适配tabbar */
}

.no-device-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
}

/* 扫码按钮 - 严格按照Figma设计 */
.scan-button {
  width: 280rpx;
  height: 280rpx;
  background-color: #f2f3ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  // box-shadow: 0px 16rpx 20rpx -10rpx rgba(0, 0, 0, 0.08),
  //             0px 32rpx 48rpx 4rpx rgba(0, 0, 0, 0.04),
  //             0px 12rpx 60rpx 10rpx rgba(0, 0, 0, 0.05);
}

.scan-button:active {
  transform: scale(0.95);
  // box-shadow: 0px 8rpx 12rpx -6rpx rgba(0, 0, 0, 0.08),
  //             0px 16rpx 24rpx 2rpx rgba(0, 0, 0, 0.04),
  //             0px 6rpx 30rpx 5rpx rgba(0, 0, 0, 0.05);
}

.scan-icon {
  width: 116rpx;
  height: 116rpx;
  /* 图标颜色转换为#366EF4 */
  // filter: brightness(0) saturate(100%) invert(27%) sepia(58%) saturate(3294%) hue-rotate(224deg) brightness(96%) contrast(93%);
}

/* 扫码文字 - 严格按照Figma设计 */
.scan-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 28rpx;
  line-height: 44rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

/* 有设备状态容器 */
.has-device-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

/* 蓝牙入口图标 */
.bluetooth-entry {
  position: fixed;
  bottom: 200rpx; /* 导航栏下方 */
  right: 32rpx;
  z-index: 999;
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  animation: bluetoothBounce 2s ease-in-out infinite;
}

@keyframes bluetoothBounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-16rpx) scale(1.05);
  }
  60% {
    transform: translateY(-8rpx) scale(1.02);
  }
}

.bluetooth-entry:active {
  animation: none;
  transform: scale(0.9);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
}

.bluetooth-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bluetooth-image {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

.scrollarea {
  flex: 1;
  padding-bottom: calc(150rpx + env(safe-area-inset-bottom)); /* 适配tabbar */
}

/* 主要内容区域 */
.main-content {
  padding: 20rpx;
  background: #ffffff;
  box-shadow: 0rpx 15rpx 19rpx -10rpx rgba(0, 0, 0, 0.08),
    0rpx 31rpx 46rpx 4rpx rgba(0, 0, 0, 0.04),
    0rpx 11rpx 57rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 0rpx 0rpx 38rpx 38rpx;
}

/* 报警状态条 */
.alert-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 20rpx 0;
  background: #f2f3ff;
  border-radius: 8rpx;
  margin: 0 30rpx;
  margin-bottom: 30rpx;
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44rpx;
  height: 44rpx;
}

.alert-text {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 28rpx;
  line-height: 44rpx;
  color: #002a7c;
  text-align: center;
}

/* 设备状态卡片 */
.device-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 46rpx;
  margin-bottom: 48rpx;
}

/* 电池容器 */
.battery-container {
  display: flex;
  align-items: center;
  gap: 0;
}

/* 电池可视化 - 正常状态 */
.battery-visual {
  position: relative;
  width: 456rpx;
  height: 240rpx;
  background: rgba(7, 217, 67, 0.1);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 电池可视化 - 低电状态 */
.battery-visual-low {
  background: rgba(255, 64, 0, 0.1);
}

.battery-bars {
  box-sizing: border-box;
  position: absolute;
  left: 1rpx;
  top: 0;
  width: 380rpx;
  height: 240rpx;
  background: linear-gradient(270deg, #07d943 0%, #65f59c 100%);
  border-radius: 20rpx 0 0 20rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx;
  box-shadow: 0px 0px 56rpx 2rpx rgba(7, 217, 67, 0.3),
    0px 0px 12rpx 0px rgba(7, 217, 67, 0.3),
    inset 0px 0px 17rpx 10rpx rgba(0, 181, 51, 0.2);
}

/* 低电状态电池条 */
.battery-bars-low {
  width: 60rpx; /* 根据设计调整宽度以显示低电量 */
  background: linear-gradient(270deg, #ff4000 0%, #ff4000 70%);
  box-shadow: 0px 0px 56rpx 2rpx rgba(255, 64, 0, 0.3),
    0px 0px 12rpx 0px rgba(255, 64, 0, 0.3),
    inset 0px 0px 17rpx 10rpx rgba(255, 64, 0, 0.2);
}

.battery-bar {
  width: 4rpx;
  height: 100%;
  background: rgba(255, 255, 255, 0.14);
  border-radius: 4rpx;
  box-shadow: 0px 0px 8rpx 0px rgba(0, 136, 38, 0.4);
}

/* 低电状态电池条 */
.battery-bar-low {
  box-shadow: 0px 0px 8rpx 0px rgba(255, 64, 0, 0.2);
}

.battery-status {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  z-index: 10;
}

.battery-percent {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 56rpx;
  line-height: 72rpx;
  color: #ffffff;
  text-shadow: 0px 4rpx 8rpx rgba(0, 136, 38, 0.6);
}

/* 低电状态百分比 */
.battery-percent-low {
  color: #f6685d;
  text-shadow: 0px 4rpx 8rpx rgba(246, 104, 93, 0.2);
}

.status-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 18rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 28rpx;
}

/* 低电状态徽章 */
.status-badge-low {
  background: rgba(255, 255, 255, 0.3);
}

.status-text {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 28rpx;
  line-height: 44rpx;
  color: #ffffff;
  text-shadow: 0px 4rpx 8rpx rgba(0, 136, 38, 0.6);
}

/* 低电状态文字 */
.status-text-low {
  color: #f6685d;
  text-shadow: 0px 4rpx 8rpx rgba(246, 104, 93, 0.2);
}

/* 电池指示器 */
.battery-indicator {
  width: 12rpx;
  height: 76rpx;
  background: #b4ecc5;
  border-radius: 0 8rpx 8rpx 0;
  box-shadow: 0px 16rpx 20rpx -10rpx rgba(0, 0, 0, 0.08),
    0px 32rpx 48rpx 4rpx rgba(0, 0, 0, 0.04),
    0px 12rpx 60rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 低电状态指示器 */
.battery-indicator-low {
  background: #ffece5;
  box-shadow: 0px 0px 56rpx 2rpx rgba(255, 64, 0, 0.2),
    0px 0px 12rpx 0px rgba(255, 64, 0, 0.3),
    inset 0px 0px 17rpx 10rpx rgba(255, 64, 0, 0.2);
}

/* 设备参数 */
.device-params {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 666rpx;
  height: 100rpx;
  padding: 0 80rpx;
  box-sizing: border-box;
}

.param-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.param-value {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 36rpx;
  line-height: 52rpx;
  color: rgba(0, 0, 0, 0.9);
}

.param-label {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 44rpx;
  color: rgba(0, 0, 0, 0.6);
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4rpx;
  padding: 16rpx 28rpx;
  background: #f2f3ff;
  border-radius: 16rpx;
  flex: 1;
  min-height: 88rpx;
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
}

.button-text {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 24rpx;
  line-height: 44rpx;
  color: var(--primaryColor);
  text-align: center;
}

/* 地图区域 */
.map-container {
  position: relative;
  height: 484rpx;
  margin: 32rpx;
  margin-top: 48rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.device-map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4rpx;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(12rpx);
  border-radius: 16rpx;
}

.location-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 54rpx;
  height: 54rpx;
}

/* 动画效果 */
.action-button {
  transition: all 0.2s ease;
}

.action-button:active {
  transform: scale(0.95);
  background: #e8e9ff;
}

.battery-bars {
  animation: battery-glow 2s ease-in-out infinite alternate;
}

/* 低电状态发光动画 */
.battery-bars-low {
  animation: battery-glow-low 2s ease-in-out infinite alternate;
}

@keyframes battery-glow {
  0% {
    box-shadow: 0px 0px 56rpx 2rpx rgba(7, 217, 67, 0.3),
      0px 0px 12rpx 0px rgba(7, 217, 67, 0.3),
      inset 0px 0px 17rpx 10rpx rgba(0, 181, 51, 0.2);
  }
  100% {
    box-shadow: 0px 0px 56rpx 2rpx rgba(7, 217, 67, 0.5),
      0px 0px 12rpx 0px rgba(7, 217, 67, 0.5),
      inset 0px 0px 17rpx 10rpx rgba(0, 181, 51, 0.3);
  }
}

@keyframes battery-glow-low {
  0% {
    box-shadow: 0px 0px 56rpx 2rpx rgba(255, 64, 0, 0.3),
      0px 0px 12rpx 0px rgba(255, 64, 0, 0.3),
      inset 0px 0px 17rpx 10rpx rgba(255, 64, 0, 0.2);
  }
  100% {
    box-shadow: 0px 0px 56rpx 2rpx rgba(255, 64, 0, 0.5),
      0px 0px 12rpx 0px rgba(255, 64, 0, 0.5),
      inset 0px 0px 17rpx 10rpx rgba(255, 64, 0, 0.4);
  }
}

.navbar-icon {
  // width: 48rpx;
  // height: 48rpx;
  // display: flex;
  // align-items: center;
  // justify-content: center;
}

/* 扫码确认弹窗样式 */
.scan-confirm-popup {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 0 32rpx 0;
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.popup-title {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 36rpx;
  line-height: 52rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

.device-info-section {
  padding: 0 48rpx;
  margin-bottom: 32rpx;
}

.device-info-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx 0;
}

.device-info-label {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
}

.device-info-divider {
  flex: 1;
  height: 0;
  border-top: 2rpx dashed #e7e7e7;
}

.device-info-value {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 28rpx;
  line-height: 32rpx;
  color: rgba(0, 0, 0, 0.9);
  white-space: nowrap;
}

.popup-buttons {
  display: flex;
  gap: 60rpx;
  padding: 0 48rpx;
}

/* 自定义按钮样式 */
.custom-cancel-btn,
.custom-confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 2rpx solid #001a57;
}

.custom-cancel-btn {
  background-color: #ffffff;
  color: #001a57;
}

.custom-cancel-btn:active {
  background-color: rgba(0, 26, 87, 0.05);
}

.custom-cancel-btn text {
  color: #001a57;
  font-size: 32rpx;
  font-weight: 500;
}

.custom-confirm-btn {
  background-color: #001a57;
  color: #ffffff;
}

.custom-confirm-btn:active {
  background-color: rgba(0, 26, 87, 0.8);
}

.custom-confirm-btn text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}

/* 序列号信息 */
.serial-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 0 30rpx;
  height: 56rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}

.serial-label {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 26rpx;
  line-height: 1;
  color: #666666;
}

.serial-number {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 26rpx;
  line-height: 1;
  color: #333333;
}

.more-link {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 26rpx;
  line-height: 1;
  color: var(--primaryColor);
  text-decoration: none;
}

/* 底部操作按钮区域 */
.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 60rpx;
  margin: 40rpx 50rpx 20rpx 50rpx;
}

.bottom-action-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
  padding: 12rpx;
  background: #ffffff;
  flex: 1;
  transition: all 0.2s ease;
}

.bottom-action-button:active {
  opacity: 0.8;
}

.bottom-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44rpx;
  height: 44rpx;
}

.bottom-button-icon image {
  width: 40rpx;
  height: 40rpx;
  // filter: invert(34%) sepia(64%) saturate(4645%) hue-rotate(214deg)
  //   brightness(101%) contrast(102%);
}

.bottom-button-text {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 36rpx;
  color: #333333;
  text-align: left;
}
.bottom-action-line {
  width: 1rpx;
  height: 44rpx;
  background: #e7e7e7;
}
