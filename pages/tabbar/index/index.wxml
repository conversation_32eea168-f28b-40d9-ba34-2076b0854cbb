<!--index.wxml-->
<t-navbar t-class-placeholder="t-navbar-placeholder" t-class-content="t-navbar-content" title="设备">
  <!-- <view slot="left">
    <image src="/images/scan.png" class="navbar-icon" bindtap="onScan" />
  </view> -->
  <!-- <view slot="left">
    <text class="navbar-icon" bindtap="onShowDeviceList">设备列表</text>
  </view> -->
</t-navbar>

<!-- Loading 状态 -->
<view wx:if="{{isLoading}}" class="loading-container">
  <view class="loading-content">
    <!-- 骨架屏 - 模拟设备卡片 -->
    <view class="skeleton-card">
      <view class="skeleton-header">
        <view class="skeleton-line skeleton-line-short"></view>
        <view class="skeleton-line skeleton-line-medium"></view>
      </view>
      <view class="skeleton-battery">
        <view class="skeleton-battery-visual"></view>
        <view class="skeleton-battery-info">
          <view class="skeleton-line skeleton-line-short"></view>
          <view class="skeleton-line skeleton-line-medium"></view>
        </view>
      </view>
      <view class="skeleton-params">
        <view class="skeleton-param">
          <view class="skeleton-line skeleton-line-short"></view>
          <view class="skeleton-line skeleton-line-mini"></view>
        </view>
        <view class="skeleton-param">
          <view class="skeleton-line skeleton-line-short"></view>
          <view class="skeleton-line skeleton-line-mini"></view>
        </view>
        <view class="skeleton-param">
          <view class="skeleton-line skeleton-line-short"></view>
          <view class="skeleton-line skeleton-line-mini"></view>
        </view>
      </view>
      <view class="skeleton-buttons">
        <view class="skeleton-button"></view>
        <view class="skeleton-button"></view>
        <view class="skeleton-button"></view>
        <view class="skeleton-button"></view>
      </view>
    </view>
    <!-- 骨架屏 - 模拟地图区域 -->
    <view class="skeleton-map"></view>
  </view>
</view>

<!-- 无设备状态 -->
<view wx:elif="{{!hasDevices}}" class="no-device-container">
  <view class="no-device-content">
    <view class="scan-button" bindtap="onScan">
      <image src="/images/home-scan.svg" class="scan-icon" />
    </view>
    <text class="scan-text">扫码添加设备</text>


  </view>
</view>

<!-- 有设备状态 -->
<view wx:else class="has-device-container">
  <!-- 蓝牙入口图标 -->
  <view class="bluetooth-entry" bindtap="onBluetoothSearch">
    <view class="bluetooth-icon">
      <image src="/images/bluetooth.svg" class="bluetooth-image" />
    </view>
  </view>
  <scroll-view class="scrollarea" scroll-y type="list">

    <!-- 主要内容区域 -->
    <view class="main-content">

      <!-- 报警状态条 -->
      <view class="alert-banner">
        <view class="alert-icon">
          <image src="/images/alert.svg" mode="aspectFit" />
        </view>
        <text class="alert-text">{{alertStatus}}</text>
      </view>

      <!-- 电池序列号信息 -->
      <view class="" style="display: flex; flex-direction: column; align-items: center;">

        <view class="serial-info">
          <text class="serial-label">电池序列号</text>
          <text class="serial-number">{{currentDevice.sn}}</text>
          <text class="more-link" bindtap="onShowMoreInfo">详情</text>
        </view>
      </view>

      <!-- 设备状态卡片 -->
      <view class="device-card">
        <!-- 电池状态可视化 -->
        <view class="battery-container">
          <view class="battery-visual battery-visual-{{batteryStatus}}">
            <view class="battery-bars battery-bars-{{batteryStatus}}">
              <view class="battery-bar battery-bar-{{batteryStatus}}" wx:for="{{batteryStatus === 'low' ? 7 : 50}}"
                wx:key="index"></view>
            </view>
            <view class="battery-status">
              <text class="battery-percent battery-percent-{{batteryStatus}}">{{batteryLevel}}%</text>
              <view class="status-badge status-badge-{{batteryStatus}}">
                <text class="status-text status-text-{{batteryStatus}}">{{deviceStatus}}</text>
              </view>
            </view>
          </view>
          <view class="battery-indicator battery-indicator-{{batteryStatus}}"></view>
        </view>

        <!-- 设备参数 -->
        <view class="device-params">
          <view class="param-item">
            <text class="param-value">{{voltage}}</text>
            <text class="param-label">电压</text>
          </view>
          <view class="param-item">
            <text class="param-value">{{current}}</text>
            <text class="param-label">电流</text>
          </view>
          <view class="param-item">
            <text class="param-value">{{power}}</text>
            <text class="param-label">功率</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-buttons">
        <view class="action-button" bindtap="onForceStart">
          <view class="button-icon">
            <image src="/images/start.svg" mode="aspectFit" tint-color="white" />
          </view>
          <text class="button-text">强制启动</text>
        </view>

        <view class="action-button" bindtap="onScheduleHeat">
          <view class="button-icon">
            <image src="/images/heating.svg" mode="aspectFit" />
          </view>
          <text class="button-text">预约加热</text>
        </view>

        <view class="action-button" bindtap="onRemoteStart">
          <view class="button-icon">
            <image src="/images/open.svg" mode="aspectFit" />
          </view>
          <text class="button-text">远程开机</text>
        </view>

        <view class="action-button" bindtap="onRemoteShutdown">
          <view class="button-icon">
            <image src="/images/shutdown.svg" mode="aspectFit" />
          </view>
          <text class="button-text">远程关机</text>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <!-- <view class="bottom-actions">
      <view class="bottom-action-button" bindtap="onUnbindDevice">
        <view class="bottom-button-icon">
          <image src="/images/unbind.svg" mode="aspectFit" />
        </view>
        <text class="bottom-button-text">解绑电池</text>
      </view>
      <view class="bottom-action-line" />
      <view class="bottom-action-button" bindtap="onScan">
        <view class="bottom-button-icon">
          <image src="/images/add.svg" mode="aspectFit" />
        </view>
        <text class="bottom-button-text">添加设备</text>
      </view>
    </view> -->
    </view>

    <!-- 地图区域 -->
    <view class="map-container">
      <map class="device-map" longitude="{{longitude}}" latitude="{{latitude}}" scale="16" show-location="{{true}}"
        markers="{{markers}}" bindtap="onMapTap">
        <view class="map-overlay">
          <view class="location-icon">
            <image src="/images/full.svg" mode="aspectFit"></image>
          </view>
        </view>
      </map>
    </view>
  </scroll-view>
</view>

<!-- 扫码设备确认弹窗 -->
<t-popup visible="{{showScanConfirmPopup}}" placement="bottom" bind:visible-change="onScanPopupVisibleChange">
  <view class="scan-confirm-popup">
    <!-- 弹窗标题 -->
    <view class="popup-header">
      <text class="popup-title">请确认该设备</text>
    </view>

    <!-- 设备信息 -->
    <view class="device-info-section">
      <view class="device-info-item">
        <text class="device-info-label">电池序列号</text>
        <view class="device-info-divider"></view>
        <text class="device-info-value">{{scannedDeviceInfo.serialNumber}}</text>
      </view>

      <view class="device-info-item">
        <text class="device-info-label">固件版本</text>
        <view class="device-info-divider"></view>
        <text class="device-info-value">{{scannedDeviceInfo.firmwareVersion}}</text>
      </view>

      <view class="device-info-item">
        <text class="device-info-label">硬件版本</text>
        <view class="device-info-divider"></view>
        <text class="device-info-value">{{scannedDeviceInfo.hardwareVersion}}</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-buttons">
      <view class="custom-cancel-btn" bindtap="onCancelDevice">
        <text>取消</text>
      </view>
      <view class="custom-confirm-btn" bindtap="onConfirmDevice">
        <text>确认</text>
      </view>
    </view>
  </view>
</t-popup>