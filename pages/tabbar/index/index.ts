// index.ts
// 获取应用实例
const app = getApp<IAppOption>();
import { onPageShow } from '../../../utils/tabbar.js';
import { getDeviceBySn } from '../../../api/device';
import { getUserDevices } from '../../../api/user';
import type { Device } from '../../../api/types';
import { eventBus } from '../../../utils/eventBus';
import authService from '../../../common/auth';
import mqtt from '../../../utils/mqtt.min.js';

// 定义扫码数据类型
interface ScanData {
  sn: string;
  [key: string]: any;
}

Page({
  data: {
    // 页面加载状态
    isLoading: true,
    // 是否有设备
    hasDevices: false,
    // 设备列表
    deviceList: [] as Device[],
    // 当前显示的设备
    currentDevice: null as Device | null,

    // 地图相关数据
    longitude: 116.397128,
    latitude: 39.916527,
    markers: [
      {
        id: 1,
        latitude: 39.916527,
        longitude: 116.397128,
        title: '设备位置',
        // iconPath: '/images/location-marker.png',
        width: 30,
        height: 30,
      },
    ],

    // 扫码确认弹窗相关
    showScanConfirmPopup: false,
    scannedDeviceInfo: {
      serialNumber: '',
      firmwareVersion: '',
      hardwareVersion: '',
      rawScanData: '',
    },

    client: null as any,
    conenctBtnText: '连接',
    host: 'mqtt.ricnsmart.com',
    subTopic: '/sys/+/+/thing/property/post',
    pubTopic: '/sys/+/+/thing/event/post',
    pubMsg: 'Hello! I am from WeChat miniprogram',
    receivedMsg: '',
    mqttOptions: {
      username: 'new_energy_platform_web',
      password: 'jpr1AMN.rhx1txy*nvm',
      reconnectPeriod: 1000, // 1000毫秒，设置为 0 禁用自动重连，两次重新连接之间的间隔时间
      connectTimeout: 30 * 1000, // 30秒，连接超时时间
      // 更多参数请参阅 MQTT.js 官网文档：https://github.com/mqttjs/MQTT.js#mqttclientstreambuilder-options
      // 更多 EMQ 相关 MQTT 使用教程可在 EMQ 官方博客中进行搜索：https://www.emqx.com/zh/blog
    },
  },

  // 私有状态管理
  _isDataLoading: false, // 防止重复加载数据
  _hasInitialized: false, // 标记是否已经初始化过
  _lastLoadTime: 0, // 上次加载时间，用于防抖

  connect() {
    // 如果已经有连接，先断开
    if (this.data.client) {
      this.disconnect();
    }

    try {
      const clientId = new Date().getTime();
      console.log('首页开始连接MQTT，clientId:', clientId);

      this.data.client = mqtt.connect(`wxs://${this.data.host}:8084/mqtt`, {
        ...this.data.mqttOptions,
        clientId,
      });

      this.data.client.on('connect', () => {
        console.log('首页MQTT 连接成功');
        this.subscribe();
      });

      this.data.client.on('message', (topic: any, payload: any) => {
        try {
          // console.log('首页MQTT 收到消息', payload + '');
          const deviceInfo = JSON.parse(payload + '');
          // console.log('首页设备信息:', deviceInfo);
          // 可以在这里处理设备状态更新
          this.handleDeviceUpdate(deviceInfo);
        } catch (parseError) {
          console.error('首页解析MQTT消息失败:', parseError);
        }
      });

      this.data.client.on('error', (error: any) => {
        console.error('首页MQTT连接错误:', error);
        this.data.client = null;
      });

      this.data.client.on('reconnect', () => {
        console.log('首页MQTT重连中...');
      });

      this.data.client.on('offline', () => {
        console.log('首页MQTT离线');
      });

      this.data.client.on('close', () => {
        console.log('首页MQTT连接关闭');
      });
    } catch (error) {
      console.error('首页MQTT连接初始化失败:', error);
      this.data.client = null;
    }
  },

  subscribe() {
    if (this.data.client && this.data.client.connected) {
      console.log('首页订阅MQTT主题:', this.data.subTopic);
      this.data.client.subscribe(this.data.subTopic);
    }
  },

  unsubscribe() {
    if (this.data.client && this.data.client.connected) {
      console.log('首页取消订阅MQTT主题:', this.data.subTopic);
      this.data.client.unsubscribe(this.data.subTopic);
    }
  },

  disconnect() {
    if (this.data.client) {
      console.log('首页断开MQTT连接');
      try {
        this.unsubscribe();
        this.data.client.end(true);
        this.data.client = null;
      } catch (error) {
        console.error('首页断开MQTT连接失败:', error);
        this.data.client = null;
      }
    }
  },

  // 处理设备状态更新
  handleDeviceUpdate(properties: any) {
    console.log('首页收到设备状态更新:', properties);
    // 更新当前设备的状态信息
    if (
      this.data.currentDevice &&
      properties.SN === this.data.currentDevice.sn
    ) {
      const packVolMv = properties.PackVol || null;
      const voltageDisplay =
        packVolMv !== null ? `${(packVolMv / 1000).toFixed(1)}V` : '0.0V';

      const packCurMa = properties.PackCur || null;
      const currentDisplay =
        packCurMa !== null ? `${(packCurMa / 1000).toFixed(1)}A` : '0.0A';

      let powerDisplay = '0.0W';
      if (packVolMv !== null && packCurMa !== null) {
        const powerW = (packVolMv * packCurMa) / 1000000;
        powerDisplay = `${powerW.toFixed(1)}W`;
      }
      this.setData({
        voltage: voltageDisplay,
        current: currentDisplay,
        power: powerDisplay,
      });
    }
  },

  // MQTT 发布消息
  publishMessage(deviceSn: string, command: string, params: any = {}) {
    // if (!this.data.client || !this.data.client.connected) {
    //   console.error('MQTT未连接，无法发送命令');
    //   wx.showToast({
    //     title: 'MQTT未连接',
    //     icon: 'none',
    //   });
    //   return false;
    // }

    // 根据文档构建发布主题: /sys/100/设备SN/thing/service/invoke
    const topic = `/sys/100/${deviceSn}/thing/service/invoke`;

    // 构建消息体
    const message = {
      [command]: params,
    };

    try {
      console.log('发布MQTT消息:', { topic, message });
      this.data.client.publish(topic, JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('发布MQTT消息失败:', error);
      wx.showToast({
        title: '发送命令失败',
        icon: 'none',
      });
      return false;
    }
  },

  onLoad() {
    console.log('首页 onLoad 开始');
    this.setupEventListeners();

    // 立即检查登录状态
    this.checkLoginAndLoadData();
    this.getCurrentLocation();

    // this.connect();
  },

  onShow() {
    console.log('首页 onShow 开始');
    // 使用工具函数更新自定义tabbar状态
    onPageShow(this);

    // 检查是否需要刷新数据
    const needRefresh = wx.getStorageSync('needRefreshDeviceList');
    if (needRefresh) {
      console.log('检测到需要刷新设备列表的标记，强制刷新数据');
      wx.removeStorageSync('needRefreshDeviceList');
      this.checkLoginAndLoadData(true);
      return;
    }

    // 只有在未初始化时才加载数据，避免重复请求
    if (!this._hasInitialized) {
      console.log('首页首次显示，开始加载数据');
      this.checkLoginAndLoadData();
    } else {
      console.log('首页已初始化，跳过重复加载');
    }
  },

  onHide() {
    console.log('首页隐藏，取消MQTT订阅');
    this.unsubscribe();
  },

  onUnload() {
    console.log('首页卸载，断开MQTT连接');
    // 页面销毁时清除事件监听和状态
    this.removeEventListeners();
    this.resetLoadingState();
    this.disconnect();
  },

  // 重置加载状态
  resetLoadingState() {
    this._isDataLoading = false;
    this._hasInitialized = false;
    this._lastLoadTime = 0;
  },

  // 设置事件监听器
  setupEventListeners() {
    const self = this;

    // 监听登录成功事件
    eventBus.on('loginSuccess', function (data: any) {
      console.log('首页收到登录成功事件:', data);
      self.onLoginSuccess(data);
    });

    // 监听登录状态更新事件
    eventBus.on('loginStatusUpdate', function (data: any) {
      console.log('首页收到登录状态更新事件:', data);
      self.onLoginStatusUpdate(data);
    });

    // 监听需要注册事件
    eventBus.on('needRegister', function (data: any) {
      console.log('首页收到需要注册事件:', data);
      self.onNeedRegister(data);
    });
  },

  // 清除事件监听器
  removeEventListeners() {},

  // 检查登录状态并加载数据
  async checkLoginAndLoadData(forceRefresh = false) {
    console.log(
      'checkLoginAndLoadData 开始检查登录状态, forceRefresh:',
      forceRefresh
    );

    // 防重复加载：如果正在加载中，直接返回
    if (this._isDataLoading && !forceRefresh) {
      console.log('数据正在加载中，跳过重复请求');
      return;
    }

    // 防抖：如果距离上次加载时间太短，跳过（除非强制刷新）
    const now = Date.now();

    // 标记开始加载
    this._isDataLoading = true;
    this._lastLoadTime = now;

    // 设置loading状态（只有在首次加载或强制刷新时才显示loading）
    if (!this._hasInitialized || forceRefresh) {
      this.setData({
        isLoading: true,
      });
    }

    try {
      // 检查全局应用状态
      const appLoginStatus = app.getLoginStatus?.();
      console.log('全局应用登录状态:', appLoginStatus);

      // 检查认证服务状态
      const authLoggedIn = authService.isLoggedIn();
      console.log('认证服务登录状态:', authLoggedIn);

      // 只要有一个状态显示已登录，就加载设备数据
      if ((appLoginStatus && appLoginStatus.isLoggedIn) || authLoggedIn) {
        console.log('检测到已登录状态，开始加载设备数据');
        await this.loadDeviceData();
      } else {
        console.log('未检测到登录状态，显示无设备页面');
        // 未登录，显示无设备状态
        this.setData({
          isLoading: false,
          hasDevices: false,
          deviceList: [],
          currentDevice: null,
        });
      }

      // 标记已初始化
      this._hasInitialized = true;
    } finally {
      // 清除加载状态
      this._isDataLoading = false;
    }
  },

  // 登录成功事件处理
  onLoginSuccess(data: any) {
    console.log('✅ 首页收到登录成功事件，开始加载设备数据:', data);
    // 登录成功后重新加载设备数据，但只有在未初始化时才强制刷新
    if (!this._hasInitialized) {
      this.checkLoginAndLoadData(true);
    } else {
      this.checkLoginAndLoadData();
    }
  },

  // 登录状态更新事件处理
  onLoginStatusUpdate(data: any) {
    console.log('🔄 首页收到登录状态更新事件:', data);

    // 如果已经初始化过，且状态没有实质性变化，跳过
    if (this._hasInitialized && data.isLoggedIn === this.data.hasDevices) {
      console.log('登录状态无实质性变化，跳过重复加载');
      return;
    }

    if (data.isLoggedIn) {
      console.log('✅ 登录状态为已登录，开始加载设备数据');
      // 已登录，加载设备数据
      this.checkLoginAndLoadData();
    } else {
      console.log('❌ 登录状态为未登录，显示无设备页面');
      // 未登录，显示无设备状态
      this.setData({
        isLoading: false,
        hasDevices: false,
        deviceList: [],
        currentDevice: null,
      });
    }
  },

  // 需要注册事件处理
  onNeedRegister(data: any) {
    console.log('📝 首页收到需要注册事件:', data);
    // 显示无设备状态，用户需要完成注册流程
    this.setData({
      isLoading: false,
      hasDevices: false,
      deviceList: [],
      currentDevice: null,
    });
  },

  // 加载设备数据
  async loadDeviceData() {
    console.log('loadDeviceData 开始加载设备数据');

    try {
      // 获取用户所有设备
      let res = await getUserDevices();
      res = { data: res }; // 确保数据格式正确
      console.log('res', res);

      if (res.data && res.data.length > 0) {
        // 有设备，显示最后一个绑定的设备
        const latestDevice = res.data[res.data.length - 1];

        // 计算设备状态信息
        const deviceStatus = this.calculateDeviceStatus(latestDevice);

        this.setData({
          isLoading: false,
          hasDevices: true,
          deviceList: res.data,
          currentDevice: latestDevice,
          ...deviceStatus,
        });

        console.log(
          '设备数据加载完成:',
          latestDevice,
          '计算状态:',
          deviceStatus
        );

        // 设备数据加载完成后，建立MQTT连接
        setTimeout(() => {
          this.connect();
        }, 1000);
      } else {
        // 没有设备，显示无设备页面
        this.setData({
          isLoading: false,
          hasDevices: false,
          deviceList: [],
          currentDevice: null,
        });
      }
    } catch (error) {
      console.error('加载设备数据失败:', error);
      // 如果请求失败，也显示无设备页面
      this.setData({
        isLoading: false,
        hasDevices: false,
        deviceList: [],
        currentDevice: null,
      });
    }
  },

  // 获取当前位置
  getCurrentLocation() {
    // 如果没有登录，则不获取位置
    if (!app.getLoginStatus?.()?.isLoggedIn) {
      return;
    }
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取位置成功:', res);
        this.setData({
          longitude: res.longitude,
          latitude: res.latitude,
          'markers[0].longitude': res.longitude,
          'markers[0].latitude': res.latitude,
        });
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        wx.showToast({
          title: '获取位置失败',
          icon: 'none',
        });
      },
    });
  },

  // 强制启动
  onForceStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要强制启动设备吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('强制启动');
        }
      },
    });
  },

  // 预约加热
  onScheduleHeat() {
    wx.navigateTo({
      url:
        '/pages/schedule-heat/schedule-heat?sn=' + this.data.currentDevice?.sn,
    });
  },

  // 远程开机
  onRemoteStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程开机吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('远程开机');
        }
      },
    });
  },

  // 远程关机
  onRemoteShutdown() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程关机吗？此操作可能会影响设备正常运行。',
      success: (res) => {
        if (res.confirm) {
          this.performDeviceAction('远程关机');
        }
      },
    });
  },

  // 蓝牙搜索
  onBluetoothSearch() {
    wx.navigateTo({
      url: '/pages/bluetooth-search/bluetooth-search',
    });
  },

  // 执行设备操作
  performDeviceAction(action: string) {
    if (!this.data.currentDevice) {
      wx.showToast({
        title: '请先选择设备',
        icon: 'none',
      });
      return;
    }

    wx.showLoading({
      title: '执行中...',
      mask: true,
    });

    const deviceSn = this.data.currentDevice.sn;
    let success = false;

    try {
      switch (action) {
        case '强制启动':
          success = this.publishMessage(deviceSn, 'PutDI', 'open');
          break;
        case '远程开机':
          // 发送 PutCtrl 命令，参数为 "open"
          success = this.publishMessage(deviceSn, 'PutCtrl', 'open');
          break;

        case '远程关机':
          // 发送 PutCtrl 命令，参数为 "close"
          success = this.publishMessage(deviceSn, 'PutCtrl', 'close');
          break;

        case '控制休眠':
          // 发送 PutSleep 命令，参数为 "open"
          success = this.publishMessage(deviceSn, 'PutSleep', 'open');
          break;

        case '立即加热':
          // 发送 PutWarm 命令，立即加热模式
          success = this.publishMessage(deviceSn, 'PutWarm', {
            Mode: 'immediate',
          });
          break;

        default:
          console.warn('未知的设备操作:', action);
          success = false;
      }

      setTimeout(() => {
        wx.hideLoading();
        if (success) {
          wx.showToast({
            title: `${action}命令已发送`,
            icon: 'none',
          });
        } else {
          wx.showToast({
            title: `${action}失败`,
            icon: 'none',
          });
        }
      }, 1000);
    } catch (error) {
      console.error('执行设备操作失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: `${action}失败`,
        icon: 'none',
      });
    }
  },

  // 地图点击事件
  onMapTap() {
    wx.openLocation({
      latitude: this.data.latitude,
      longitude: this.data.longitude,
      name: '设备位置',
      address: '设备详细地址',
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('用户下拉刷新，强制重新加载数据');
    // 下拉刷新时强制重新加载数据
    this.checkLoginAndLoadData(true);
    this.getCurrentLocation();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '设备监控 - 实时查看设备状态',
      path: '/pages/index/index',
      imageUrl: '/images/share-image.jpg',
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '设备监控 - 智能设备管理',
      query: '',
      imageUrl: '/images/share-timeline.jpg',
    };
  },

  onScan() {
    // 检查登录状态
    const loginStatus = app.getLoginStatus?.();
    if (!loginStatus || !loginStatus.isLoggedIn) {
      // 未登录，先执行登录流程
      this.handleLoginBeforeScan();
      return;
    }

    // 已登录，执行扫码
    this.performScan();
  },

  // 处理扫码前的登录
  async handleLoginBeforeScan() {
    try {
      wx.showLoading({
        title: '登录中...',
        mask: true,
      });

      // 执行登录
      const loginSuccess = await app.login?.();

      if (loginSuccess) {
        wx.hideLoading();
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1000,
        });

        // 登录成功后延迟一点再执行扫码
        setTimeout(() => {
          this.performScan();
        }, 1000);
      } else {
        wx.hideLoading();
        wx.showModal({
          title: '登录失败',
          content: '请检查网络连接或稍后重试',
          showCancel: false,
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('登录过程出错:', error);
      wx.showModal({
        title: '登录失败',
        content: '登录过程出现错误，请稍后重试',
        showCancel: false,
      });
    }
  },

  // 执行扫码
  performScan() {
    wx.scanCode({
      onlyFromCamera: false,
      success: (res) => {
        console.log('扫码结果:', res);
        const scannedData = res.result;
        console.log('扫码数据:', scannedData);

        // 模拟设备识别过程
        this.recognizeDevice(scannedData);
      },
      fail: (err) => {
        console.error('扫码失败:', err);
      },
    });
  },

  // 设备识别
  async recognizeDevice(scanData: string) {
    wx.showLoading({
      title: '识别设备中...',
      mask: true,
    });

    try {
      // 解析扫码数据
      const parsedScanData: ScanData = scanData.startsWith('{')
        ? JSON.parse(scanData)
        : { sn: scanData };

      // 通过API验证设备是否存在
      if (!/^[0-9a-zA-Z]+$/.test(parsedScanData.sn)) {
        throw new Error();
      }
      wx.navigateTo({
        url: `/pages/scan-device-info/scan-device-info?deviceId=${parsedScanData.sn}`,
      });
      return;
      let res = await getDeviceBySn(parsedScanData.sn);
      console.log('设备信息:', res);
      res = { data: res };

      if (res.data) {
        // 设备识别成功
        const deviceInfo = {
          serialNumber: parsedScanData.sn,
          firmwareVersion: res.data?.properties?.FirmwareVersion
            ? `V ${res.data.properties.FirmwareVersion}`
            : '未知版本',
          hardwareVersion: res.data?.properties?.HardwareVersion
            ? `V ${res.data.properties.HardwareVersion}`
            : '未知版本',
          rawScanData: JSON.stringify(parsedScanData),
        };

        console.log('准备显示弹窗，设备信息:', deviceInfo);

        // 更新数据并显示确认弹窗
        this.setData(
          {
            scannedDeviceInfo: deviceInfo,
            showScanConfirmPopup: true,
          },
          () => {
            console.log('弹窗状态已更新:', this.data.showScanConfirmPopup);
          }
        );
      } else {
        // 设备不存在或获取失败
        throw new Error(res.message || '设备不存在');
      }
    } catch (error: any) {
      console.error('设备识别失败:', error);
      wx.showModal({
        title: '',
        content:
          error.message || '未能识别该设备，请检查二维码是否为有效的设备码',
        showCancel: false,
        confirmText: '确定',
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 确认设备 - 跳转到设备信息页面
  onConfirmDevice() {
    const { scannedDeviceInfo } = this.data;

    this.setData({
      showScanConfirmPopup: false,
    });

    // 跳转到扫码设备信息页面，让用户进行绑定操作
    wx.navigateTo({
      url: `/pages/scan-device-info/scan-device-info?deviceId=${scannedDeviceInfo.serialNumber}`,
    });
  },

  // 取消确认
  onCancelDevice() {
    this.setData({
      showScanConfirmPopup: false,
    });
  },

  // 弹窗可见性变化
  onScanPopupVisibleChange(e: any) {
    this.setData({
      showScanConfirmPopup: e.detail.visible,
    });
  },

  onShowDeviceList() {
    wx.navigateTo({
      url: '/pages/device-list/device-list',
    });
  },

  onShowMoreInfo() {
    wx.navigateTo({
      url: '/pages/tabbar/detail/detail?sn=' + this.data.currentDevice?.sn,
    });
  },

  // 解绑设备
  onUnbindDevice() {
    wx.showModal({
      title: '确认解绑',
      content: '确定要解绑当前设备吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '解绑成功',
            icon: 'success',
          });
          // TODO: 实现解绑逻辑
          // 解绑成功后强制重新加载设备列表
          this.checkLoginAndLoadData(true);
        }
      },
    });
  },

  // 计算设备状态信息（静态方法）
  calculateDeviceStatus(device: Device) {
    const properties = device?.properties || {};

    // 获取电池电量 - 使用SOC字段
    const batteryLevel = properties.SOC || 0;

    // 获取电压 - 使用PackVol字段（单位mV，需要转换为V）
    const packVolMv = properties.PackVol || null;
    const voltageDisplay =
      packVolMv !== null ? `${(packVolMv / 1000).toFixed(1)}V` : '0.0V';

    // 获取电流 - 使用PackCur字段（单位mA，需要转换为A）
    const packCurMa = properties.PackCur || null;
    const currentDisplay =
      packCurMa !== null ? `${(packCurMa / 1000).toFixed(1)}A` : '0.0A';

    // 计算功率 - 电压(V) × 电流(A) = 功率(W)
    let powerDisplay = '0.0W';
    if (packVolMv !== null && packCurMa !== null) {
      // PackVol单位是mV，PackCur单位是mA
      // 功率(W) = (PackVol/1000) × (PackCur/1000) = PackVol × PackCur / 1000000
      const powerW = (packVolMv * packCurMa) / 1000000;
      powerDisplay = `${powerW.toFixed(1)}W`;
    }

    // 获取在线状态
    const isOnline = device?.online || false;
    let deviceStatus = '离线';
    let batteryStatus = 'normal';

    if (isOnline) {
      if (batteryLevel <= 20) {
        deviceStatus = '在线·低电';
        batteryStatus = 'low';
      } else {
        deviceStatus = '在线·正常';
        batteryStatus = 'normal';
      }
    }

    // 获取报警状态
    const alertStatus =
      properties.AlertStatus ||
      properties.alertStatus ||
      properties.Alert ||
      '系统正常';

    return {
      deviceName: device?.name || device?.sn || '未知设备',
      deviceStatus,
      batteryLevel,
      batteryStatus,
      voltage: voltageDisplay,
      current: currentDisplay,
      power: powerDisplay,
      alertStatus,
      isOnline,
    };
  },
});
