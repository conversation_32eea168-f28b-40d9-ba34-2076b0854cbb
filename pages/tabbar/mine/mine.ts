// pages/mine/mine.ts
import { onPageShow } from '../../../utils/tabbar.js';
import loginHelper from '../../../common/loginHelper';
import { updateMe } from '../../../api/user';
import authService from '../../../common/auth';
import { eventBus } from '../../../utils/eventBus';
import storage from '../../../common/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoggedIn: false,
    userInfo: {
      avatar: '',
      nickname: '',
      mobile: '',
    },
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    console.log('我的页面加载');
    // 初始化页面登录状态
    this.initPageLogin();
    // 设置用户信息更新事件监听
    this.setupUserInfoListener();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('我的页面显示');
    // 重新检查登录状态
    this.checkLoginStatus();
    // 统一使用工具函数更新自定义tabbar状态
    onPageShow(this);
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 移除用户信息更新事件监听
    this.removeUserInfoListener();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新用户信息
    this.refreshUserInfo();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  /**
   * 设置用户信息更新事件监听
   */
  setupUserInfoListener() {
    // 定义事件处理函数
    const userInfoUpdateHandler = (data: any) => {
      console.log('个人中心页面收到用户信息更新事件:', data);
      if (data.userInfo) {
        this.setData({
          userInfo: {
            avatar: data.userInfo.avatar || '',
            nickname: data.userInfo.nickname || '',
            mobile: data.userInfo.mobile || '',
          },
        });
      }
    };

    // 将处理函数保存到data中，这样就可以在其他地方访问
    this.setData({
      _userInfoUpdateHandler: userInfoUpdateHandler,
    });

    eventBus.on('userInfoUpdate', userInfoUpdateHandler);
  },

  /**
   * 移除用户信息更新事件监听
   */
  removeUserInfoListener() {
    const handler = (this.data as any)._userInfoUpdateHandler;
    if (handler) {
      eventBus.off('userInfoUpdate', handler);
    }
  },

  /**
   * 初始化页面登录状态
   */
  initPageLogin() {
    loginHelper.initPageLogin(this, {
      onLoginSuccess: (userInfo) => {
        console.log('登录成功，用户信息:', userInfo);
        this.setData({
          isLoggedIn: true,
          userInfo: {
            avatar: userInfo.avatar || '',
            nickname: userInfo.nickname || '',
            mobile: userInfo.mobile || '',
          },
        });
      },
      onLoginFailed: () => {
        console.log('登录失败');
        this.setData({
          isLoggedIn: false,
          userInfo: {
            avatar: '',
            nickname: '',
            mobile: '',
          },
        });
      },
      onNeedRegister: (sessionData) => {
        console.log('需要注册:', sessionData);
        // 跳转到注册页面或显示注册提示
        wx.showModal({
          title: '提示',
          content: '您还未注册，是否前往注册？',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/profile-edit/profile-edit?from=register',
              });
            }
          },
        });
      },
    });
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const loginStatus = loginHelper.getLoginStatus();
    this.setData({
      isLoggedIn: loginStatus.isLoggedIn,
      userInfo: loginStatus.isLoggedIn
        ? {
            avatar: loginStatus.userInfo?.avatar || '',
            nickname: loginStatus.userInfo?.nickname || '',
            mobile: loginStatus.userInfo?.mobile || '',
          }
        : {
            avatar: '',
            nickname: '',
            mobile: '',
          },
    });
  },

  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    if (!this.data.isLoggedIn) {
      wx.stopPullDownRefresh();
      return;
    }

    try {
      const success = await loginHelper.refreshUserInfo();
      if (success) {
        this.checkLoginStatus();
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
        });
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
      });
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 登录按钮点击事件
   */
  async onLogin() {
    try {
      wx.showLoading({ title: '登录中...' });
      const success = await loginHelper.manualLogin();
      if (success) {
        this.checkLoginStatus();
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 头像点击事件 - 上传头像
   */
  async onAvatarTap() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      return;
    }

    try {
      // 选择图片
      const chooseResult = await wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        sizeType: ['compressed'],
      });
      console.log('chooseResult', chooseResult);

      if (chooseResult.tempFiles && chooseResult.tempFiles.length > 0) {
        const tempFilePath = chooseResult.tempFiles[0].tempFilePath;
        await this.uploadAvatar(tempFilePath);
      }
    } catch (error) {
      console.error('选择图片失败:', error);
      wx.showToast({
        title: '选择图片失败',
        icon: 'none',
      });
    }
  },

  /**
   * 上传头像
   */
  async uploadAvatar(filePath: string) {
    try {
      wx.showLoading({ title: '上传中...' });

      const uploadResult = await this.uploadFile(filePath);
      if (uploadResult && uploadResult.url) {
        await updateMe({ avatar: uploadResult.url });

        await this.refreshUserInfo();
        this.checkLoginStatus();

        wx.showToast({
          title: '头像更新成功',
          icon: 'success',
        });
      }
    } catch (error) {
      console.error('上传头像失败:', error);
      wx.showToast({
        title: '上传失败',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 文件上传方法（需要根据实际后端接口调整）
   */
  uploadFile(filePath: string): Promise<{ url: string }> {
    return new Promise((resolve, reject) => {
      const fileName = filePath.split('/').pop();
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
      const day = String(date.getDate()).padStart(2, '0');
      const localDate = `${year}-${month}-${day}`;
      const key = `wxmp/avatar/${localDate}/${fileName}`;
      const keyWithoutExt = fileName?.split('.').slice(0, -1).join('.') || '';

      const formData = {
        key,
        file: fileName,
        // timestamp: Date.now().toString(),
      };

      console.log('上传文件信息:', formData);

      const accessToken = storage.getAccessToken();
      const uploadUrl = `https://energy.ricnsmart.com/wxmp/api//v1/object`;

      wx.uploadFile({
        url: uploadUrl,
        filePath: filePath,
        name: 'file',
        formData,
        header: {
          Authorization: `Bearer ${accessToken?.token || ''}`,
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code == 0) {
              resolve({ url: data?.data?.url || '' });
              wx.showToast({
                title: '上传成功',
                icon: 'none',
              });
            } else {
              reject(new Error(data.message || '上传失败'));
            }
          } catch (e) {
            reject(new Error('解析响应失败'));
          }
        },
        fail: (error) => {
          reject(error);
        },
      });
    });
  },

  /**
   * 充电记录点击事件
   */
  onChargingRecord() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      return;
    }

    console.log('点击充电记录');
    wx.navigateTo({
      url: '/pages/charging-record/charging-record',
    });
  },

  /**
   * 编辑个人信息点击事件
   */
  onEditProfile() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      return;
    }

    console.log('点击编辑个人信息');
    wx.navigateTo({
      url: '/pages/profile-edit/profile-edit',
    });
  },

  /**
   * 菜单项点击事件
   */
  onMenuTap(e: any) {
    const { type } = e.currentTarget.dataset;
    console.log('菜单点击:', type);

    switch (type) {
      case 'contact':
        wx.showToast({
          title: '联系我们功能开发中',
          icon: 'none',
        });
        break;
      case 'help':
        wx.showToast({
          title: '使用说明功能开发中',
          icon: 'none',
        });
        break;
      default:
        break;
    }
  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 调用登录辅助工具的登出方法
          loginHelper.logout();

          // 更新页面状态
          this.setData({
            isLoggedIn: false,
            userInfo: {
              avatar: '',
              nickname: '',
              mobile: '',
            },
          });

          wx.showToast({
            title: '退出成功',
            icon: 'success',
          });
        }
      },
    });
  },
});
