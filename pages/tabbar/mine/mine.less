/* pages/mine/mine.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f6f7;
}

.page-container {
  flex: 1;
  overflow-y: hidden;
}

.content {
  padding: 25px;
  padding-bottom: 0;
}

.user-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px 0;
  margin-bottom: 17px;

  .user-info-container {
    display: flex;
    align-items: center;
    padding: 0 16px;
    // margin-bottom: 20px;
  }

  .user-avatar-container {
    margin-right: 16px;
  }

  .user-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    cursor: pointer;

    image {
      width: 100%;
      height: 100%;
    }

    .avatar-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .upload-text {
        color: white;
        font-size: 12px;
        text-align: center;
      }
    }

    &:hover .avatar-mask {
      opacity: 1;
    }

    &.default {
      background-color: #d9e1ff;
      display: flex;
      align-items: center;
      justify-content: center;

      .avatar-icon {
        width: 64px;
        height: 64px;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .user-details {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .user-name-section {
    .user-name {
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
      margin-bottom: 2px;
    }

    .user-phone-container {
      display: flex;
      align-items: center;
      gap: 2px;

      .phone-icon {
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          width: 40rpx;
          height: 40rpx;
          background-color: #f3f3f3;
          border-radius: 6px;
          padding: 4rpx;
        }
      }

      .phone-number {
        font-size: 12px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.9);
        line-height: 20px;
      }
    }
  }

  .login-prompt {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .prompt-text {
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
    }

    .login-btn {
      background-color: #001a57;
      color: white;
      border: none;
      border-radius: 20px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      min-width: 80px;

      &:after {
        border: none;
      }

      &:active {
        background-color: #001440;
      }
    }
  }

  .edit-section {
    display: flex;
    align-items: center;
    gap: 4px;

    .edit-icon {
      width: 18px;
      height: 18px;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .edit-text {
      font-size: 16px;
      font-weight: 600;
      color: #001a57;
      line-height: 24px;
    }
  }

  .divider {
    height: 0.5px;
    background-color: #e7e7e7;
    margin: 0 16px;
  }

  .charging-record-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px;
    height: 68px;
    justify-content: center;

    .record-icon {
      width: 40px;
      height: 40px;
      background-color: #f3f3f3;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 18px;
        height: 18px;
      }
    }

    .record-text {
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      line-height: 20px;
      text-align: center;
    }
  }
}

.menu-section {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 16px 16px 16px 0;
    margin-left: 16px;
    border-bottom: 0.5px solid #e7e7e7;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f8f8;
    }

    &.logout-item {
      .logout-text {
        color: #ff4757;
      }
    }

    .menu-icon {
      width: 24px;
      height: 24px;
      margin-right: 12px;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .menu-text {
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
    }

    .menu-arrow {
      width: 24px;
      height: 24px;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
