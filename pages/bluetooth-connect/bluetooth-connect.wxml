<!--pages/bluetooth-connect/bluetooth-connect.wxml-->
<t-navbar title="蓝牙连接" left-arrow />
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 设备信息卡片 -->
    <view class="device-card">
      <view class="device-header">
        <view class="device-icon {{isConnected ? 'connected' : ''}}">
          <image src="/images/mobile.svg" class="device-image" />
          <view class="connection-status {{isConnected ? 'active' : ''}}"></view>
        </view>
        <view class="device-info">
          <view class="device-name">{{deviceName}}</view>
          <view class="device-id">{{deviceId}}</view>
          <view class="connection-text {{isConnected ? 'connected' : 'disconnected'}}">
            {{isConnected ? '已连接' : '未连接'}}
          </view>
        </view>
      </view>

      <!-- 连接状态 -->
      <view class="connection-progress" wx:if="{{isConnecting}}">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{connectProgress}}%;"></view>
        </view>
        <view class="progress-text">{{connectStatusText}}</view>
      </view>

      <!-- 连接状态统计 -->
      <view class="connection-stats" wx:if="{{isConnected}}">
        <view class="stat-item">
          <text class="stat-value">{{receivedCount}}</text>
          <text class="stat-label">已接收</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{connectionTime}}</text>
          <text class="stat-label">连接时长</text>
        </view>
        <view class="stat-item">
          <view class="status-dot {{dataReceiving ? 'receiving' : ''}}"></view>
          <text class="stat-label">{{dataReceiving ? '接收中' : '等待数据'}}</text>
        </view>
      </view>
    </view>



    <!-- 单体电压信息卡片 -->
    <view class="voltage-info-card" wx:if="{{isConnected && voltageRows.length > 0}}">
      <!-- 卡片标题 -->
      <view class="card-header">
        <view class="title-line dashed"></view>
        <view class="title-content">
          <image class="title-icon" src="/images/voltmeter.svg" mode="aspectFit"></image>
          <text class="title-text">单体电压信息</text>
        </view>
        <view class="title-line dashed"></view>
      </view>

      <!-- 电压单元网格 -->
      <view class="voltage-grid">
        <view class="voltage-row" wx:for="{{voltageRows}}" wx:key="index" wx:for-index="rowIndex">
          <view class="voltage-cell" wx:for="{{item}}" wx:key="index" wx:for-item="cell">
            <view class="cell-header">
              <text class="cell-number">{{cell.number}}</text>
            </view>
            <view class="cell-divider"></view>
            <view class="cell-value">
              <text class="value-number">{{cell.voltage}}</text>
              <text class="value-unit">mV</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 温度信息卡片 -->
    <view class="temperature-info-card" wx:if="{{isConnected }}">
      <!-- 卡片标题 -->
      <view class="card-header">
        <view class="title-line dashed"></view>
        <view class="title-content">
          <image class="title-icon temperature" src="/images/temperature.svg" mode="aspectFit"></image>
          <text class="title-text">温度信息</text>
        </view>
        <view class="title-line dashed"></view>
      </view>

      <!-- 温度单元网格 -->
      <view class="temperature-grid">
        <view class="temperature-row" wx:for="{{temperatureRows}}" wx:key="index" wx:for-index="rowIndex">
          <view class="temperature-cell" wx:for="{{item}}" wx:key="index" wx:for-item="temp">
            <view class="cell-header temperature">
              <text class="cell-number">{{temp.number}}</text>
            </view>
            <view class="cell-divider"></view>
            <view class="cell-value">
              <text class="value-number">{{temp.temperature}}</text>
              <text class="value-unit">℃</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 电池状态信息卡片 -->
    <view class="battery-status-info-card" wx:if="{{isConnected }}">
      <!-- 卡片标题 -->
      <view class="card-header">
        <view class="title-line dashed"></view>
        <view class="title-content">
          <image class="title-icon" src="/images/bat.png" mode="aspectFit"></image>
          <text class="title-text">电池状态</text>
        </view>
        <view class="title-line dashed"></view>
      </view>

      <!-- 电池状态列表 -->
      <view class="battery-status-grid">
        <view class="battery-status-item" wx:for="{{batteryStatusList}}" wx:key="index">
          <view class="status-label">{{item.label}}</view>
          <view class="status-divider"></view>
          <view class="status-value" data-active="{{item.value}}">{{item.display}}</view>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 - 只在连接成功后显示 -->
    <view class="action-buttons" wx:if="{{isConnected}}">
      <view class="action-button" bindtap="onForceStart">
        <view class="button-icon">
          <image src="/images/start.svg" mode="aspectFit" tint-color="white" />
        </view>
        <text class="button-text">强制启动</text>
      </view>

      <view class="action-button" bindtap="onScheduleHeat">
        <view class="button-icon">
          <image src="/images/heating.svg" mode="aspectFit" />
        </view>
        <text class="button-text">预约加热</text>
      </view>

      <view class="action-button" bindtap="onRemoteStart">
        <view class="button-icon">
          <image src="/images/open.svg" mode="aspectFit" />
        </view>
        <text class="button-text">远程开机</text>
      </view>

      <view class="action-button" bindtap="onRemoteShutdown">
        <view class="button-icon">
          <image src="/images/shutdown.svg" mode="aspectFit" />
        </view>
        <text class="button-text">远程关机</text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-action-buttons">
      <button class="action-button danger" bindtap="disconnect" wx:if="{{isConnected}}">
        断开连接
      </button>
      <button class="action-button primary" bindtap="connect" wx:if="{{!isConnected && !isConnecting}}">
        连接设备
      </button>
      <button class="action-button secondary" bindtap="goBack">
        返回搜索
      </button>
    </view>
  </view>
</scroll-view>