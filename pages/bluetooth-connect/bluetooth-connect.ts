// pages/bluetooth-connect/bluetooth-connect.ts

interface DataItem {
  timestamp: number;
  timeStr: string;
  type: 'text' | 'hex' | 'json';
  data: string;
  hexData?: string;
  jsonData?: string;
}

interface BatteryInfo {
  level: number;
  status: string;
  serialNumber: string;
}

interface VoltageCell {
  number: string;
  voltage: string;
}

interface TemperatureCell {
  number: string;
  temperature: string;
}

interface BatteryStatusItem {
  label: string;
  value: boolean;
  display: string;
}

Page({
  data: {
    deviceId: '',
    deviceName: '',
    isConnected: false,
    isConnecting: false,
    connectProgress: 0,
    connectStatusText: '',

    // 蓝牙连接相关
    serviceId: '', // 存储服务ID
    characteristicId: '', // 存储特征值ID（用于写入数据）

    // 数据接收相关
    dataReceiving: false,
    receivedCount: 0,
    connectionTime: '00:00:00',
    connectStartTime: 0,

    // 设备数据
    batteryInfo: {
      level: 0,
      status: '连接中...',
      serialNumber: '',
    } as BatteryInfo,

    // 电池状态样式控制
    batteryStatus: 'normal', // 'normal' 或 'low'

    // 电压数据
    voltageRows: [] as VoltageCell[][],

    // 温度数据
    temperatureRows: [] as TemperatureCell[][],

    // 电池状态数据
    batteryStatusList: [] as BatteryStatusItem[],

    // 定时器
    connectionTimer: null as NodeJS.Timeout | null,
    dataRateTimer: null as NodeJS.Timeout | null,
    lastDataCount: 0,
  },

  onLoad(options: any) {
    console.log('页面参数:', options);

    if (options.deviceId && options.deviceName) {
      this.setData({
        deviceId: options.deviceId,
        deviceName: decodeURIComponent(options.deviceName),
      });

      // 自动开始连接
      setTimeout(() => {
        this.connect();
      }, 500);
    }
  },

  onUnload() {
    this.cleanup();
  },

  // 连接设备
  connect() {
    if (this.data.isConnecting || this.data.isConnected) {
      return;
    }

    this.setData({
      isConnecting: true,
      connectProgress: 0,
      connectStatusText: '正在连接...',
    });

    this.updateConnectProgress(20, '初始化连接');

    // 连接到设备
    wx.createBLEConnection({
      deviceId: this.data.deviceId,
      success: (res) => {
        console.log('连接成功', res);
        this.updateConnectProgress(60, '连接成功，获取服务');
        this.getServices();
      },
      fail: (err) => {
        console.error('连接失败', err);
        this.handleConnectionError(err);
      },
    });
  },

  // 获取服务
  getServices() {
    wx.getBLEDeviceServices({
      deviceId: this.data.deviceId,
      success: (res) => {
        console.log('获取服务成功', res);
        this.updateConnectProgress(80, '获取特征值');

        if (res.services.length > 0) {
          // 使用第一个服务
          const serviceId = res.services[0].uuid;
          this.setData({ serviceId });
          this.getCharacteristics(serviceId);
        } else {
          this.handleConnectionError({ errMsg: '未找到可用服务' });
        }
      },
      fail: (err) => {
        console.error('获取服务失败', err);
        this.handleConnectionError(err);
      },
    });
  },

  // 获取特征值
  getCharacteristics(serviceId: string) {
    wx.getBLEDeviceCharacteristics({
      deviceId: this.data.deviceId,
      serviceId: serviceId,
      success: (res) => {
        console.log('获取特征值成功', res);
        this.updateConnectProgress(90, '启用通知');

        // 找到支持write的特征值（用于发送数据）
        const writeCharacteristic = res.characteristics.find(
          (char) => char.properties.write
        );

        if (writeCharacteristic) {
          this.setData({ characteristicId: writeCharacteristic.uuid });
        }

        // 找到支持notify的特征值（用于接收数据）
        const notifyCharacteristic = res.characteristics.find(
          (char) => char.properties.notify || char.properties.indicate
        );

        if (notifyCharacteristic) {
          this.enableNotification(serviceId, notifyCharacteristic.uuid);
        } else {
          // 如果没有notify特征值，也算连接成功，但不能接收数据
          this.onConnectionSuccess();
        }
      },
      fail: (err) => {
        console.error('获取特征值失败', err);
        this.handleConnectionError(err);
      },
    });
  },

  // 启用通知
  enableNotification(serviceId: string, characteristicId: string) {
    wx.notifyBLECharacteristicValueChange({
      deviceId: this.data.deviceId,
      serviceId: serviceId,
      characteristicId: characteristicId,
      state: true,
      success: (res) => {
        console.log('启用通知成功', res);
        this.updateConnectProgress(100, '连接完成');
        this.setupDataListener();
        this.onConnectionSuccess();
      },
      fail: (err) => {
        console.error('启用通知失败', err);
        // 即使通知启用失败，也算连接成功
        this.onConnectionSuccess();
      },
    });
  },

  // 连接成功
  onConnectionSuccess() {
    this.setData({
      isConnected: true,
      isConnecting: false,
      connectProgress: 100,
      connectStatusText: '连接成功',
      connectStartTime: Date.now(),
    });

    this.startConnectionTimer();
    this.startDataRateTimer();

    wx.showToast({
      title: '连接成功',
      icon: 'success',
    });

    // 监听连接状态变化
    wx.onBLEConnectionStateChange((res) => {
      console.log('连接状态变化', res);
      if (!res.connected) {
        this.onConnectionLost();
      }
    });
  },

  // 连接丢失
  onConnectionLost() {
    this.setData({
      isConnected: false,
      dataReceiving: false,
    });

    this.stopTimers();

    wx.showToast({
      title: '连接已断开',
      icon: 'none',
    });
  },

  // 更新连接进度
  updateConnectProgress(progress: number, statusText: string) {
    this.setData({
      connectProgress: progress,
      connectStatusText: statusText,
    });
  },

  // 处理连接错误
  handleConnectionError(err: any) {
    this.setData({
      isConnecting: false,
      connectProgress: 0,
      connectStatusText: '连接失败',
    });

    let message = '连接失败';
    if (err.errMsg) {
      message = err.errMsg;
    }

    wx.showModal({
      title: '连接失败',
      content: message,
      showCancel: false,
    });
  },

  // 设置数据监听
  setupDataListener() {
    wx.onBLECharacteristicValueChange((res) => {
      console.log('收到蓝牙数据', res);
      this.handleReceivedData(res.value);
    });
  },

  // 处理接收到的数据
  handleReceivedData(arrayBuffer: ArrayBuffer) {
    this.setData({
      dataReceiving: true,
      receivedCount: this.data.receivedCount + 1,
    });

    try {
      // 将ArrayBuffer转换为字符串
      const uint8Array = new Uint8Array(arrayBuffer);
      const textData = String.fromCharCode(...uint8Array);

      console.log('接收到的原始数据:', textData);

      // 尝试解析JSON数据
      let parsedData;
      try {
        parsedData = JSON.parse(textData);
        console.log('解析后的JSON数据:', parsedData);

        // 如果数据包含设备属性，解析并更新界面
        if (parsedData && typeof parsedData === 'object') {
          this.parseDeviceProperties(parsedData);
        }
      } catch (e) {
        console.log('数据不是有效的JSON格式');
      }
    } catch (error) {
      console.error('处理接收数据失败:', error);
    }

    // 1秒后停止接收动画
    setTimeout(() => {
      this.setData({ dataReceiving: false });
    }, 1000);
  },

  // 解析设备属性数据
  parseDeviceProperties(properties: Record<string, any>) {
    try {
      // 解析电池基本信息
      const batteryInfo = {
        level: properties.SOC || 0, // 电量百分比
        status: this.getBatteryStatus(properties), // 电池状态
        serialNumber: properties.SN || this.data.deviceName, // 设备序列号
      };

      // 解析单体电压数据 (CellVol1-CellVol8)
      const voltageRows = [];
      const cellCount = 8; // 根据API数据，有8个单体电压

      for (let i = 0; i < cellCount; i += 2) {
        const row = [];
        const cellIndex1 = i + 1;
        const cellIndex2 = i + 2;

        // 第一个电压
        if (properties[`CellVol${cellIndex1}`] !== undefined) {
          row.push({
            number: `${cellIndex1}#`,
            voltage: properties[`CellVol${cellIndex1}`].toString(),
          });
        }

        // 第二个电压（如果存在）
        if (
          cellIndex2 <= cellCount &&
          properties[`CellVol${cellIndex2}`] !== undefined
        ) {
          row.push({
            number: `${cellIndex2}#`,
            voltage: properties[`CellVol${cellIndex2}`].toString(),
          });
        }

        if (row.length > 0) {
          voltageRows.push(row);
        }
      }

      // 解析温度数据
      const temperatureRows = [];

      // 第一行：1# 和 接线柱-1
      temperatureRows.push([
        {
          number: '1#',
          temperature: (properties.BatteryT1 || 0).toString(),
        },
        {
          number: '接线柱-1',
          temperature: (properties.JpT1 || 0).toString(),
        },
      ]);

      // 第二行：2# 和 接线柱-2
      temperatureRows.push([
        {
          number: '2#',
          temperature: (properties.BatteryT2 || 0).toString(),
        },
        {
          number: '接线柱-2',
          temperature: (properties.JpT2 || 0).toString(),
        },
      ]);

      // 第三行：3# 和 环境温度
      temperatureRows.push([
        {
          number: '3#',
          temperature: (properties.BatteryT3 || 0).toString(),
        },
        {
          number: '环境温度',
          temperature: (properties.AmbientT || 0).toString(),
        },
      ]);

      // 第四行：4#
      temperatureRows.push([
        {
          number: '4#',
          temperature: (properties.BatteryT4 || 0).toString(),
        },
      ]);

      // 解析电池状态数据
      const batteryStatusList: BatteryStatusItem[] = [
        {
          label: '总体过压',
          value: properties.PackOVing || false,
          display: properties.PackOVing ? '是' : '否',
        },
        {
          label: '总体欠压',
          value: properties.PackUVWarning || false,
          display: properties.PackUVWarning ? '是' : '否',
        },
        {
          label: '单体过压',
          value: properties.CellOVWarning || false,
          display: properties.CellOVWarning ? '是' : '否',
        },
        {
          label: '单体欠压',
          value: properties.CellUVWarning || false,
          display: properties.CellUVWarning ? '是' : '否',
        },
        {
          label: '电芯 充电高温',
          value: properties.McuOTCWarning || false,
          display: properties.McuOTCWarning ? '是' : '否',
        },
        {
          label: '电芯 充电低温',
          value: properties.McuUTCWarning || false,
          display: properties.McuUTCWarning ? '是' : '否',
        },
        {
          label: '电芯 放电高温',
          value: properties.McuOTDWarning || false,
          display: properties.McuOTDWarning ? '是' : '否',
        },
        {
          label: '电芯 放电低温',
          value: properties.McuUTDWarning || false,
          display: properties.McuUTDWarning ? '是' : '否',
        },
        {
          label: '接线柱 充电高温',
          value: properties.JpOTC || false,
          display: properties.JpOTC ? '是' : '否',
        },
        {
          label: '接线柱 放电高温',
          value: properties.JpOTD || false,
          display: properties.JpOTD ? '是' : '否',
        },
        {
          label: '充电状态',
          value: properties.ChargeStatus || false,
          display: properties.ChargeStatus ? '是' : '否',
        },
        {
          label: '放电状态',
          value: properties.DischargeStatus || false,
          display: properties.DischargeStatus ? '是' : '否',
        },
        {
          label: '待机状态',
          value: properties.DIStatus || false,
          display: properties.DIStatus ? '是' : '否',
        },
        {
          label: '继电器 开启',
          value: properties.CtrlStatus || false,
          display: properties.CtrlStatus ? '是' : '否',
        },
        {
          label: '加热 关闭',
          value: properties.WarmStatus || false,
          display: properties.WarmStatus ? '是' : '否',
        },
        {
          label: '短路',
          value: properties.SC || false,
          display: properties.SC ? '是' : '否',
        },
        {
          label: '低SOC',
          value: properties.lowSOC || false,
          display: properties.lowSOC ? '是' : '否',
        },
      ];

      // 计算电池状态样式
      const batteryStatus = batteryInfo.level <= 20 ? 'low' : 'normal';

      this.setData({
        batteryInfo,
        batteryStatus,
        voltageRows,
        temperatureRows,
        batteryStatusList,
      });

      console.log('蓝牙设备属性解析完成:', {
        batteryInfo,
        voltageRows: voltageRows.length,
        temperatureRows: temperatureRows.length,
        batteryStatusList: batteryStatusList.length,
      });
    } catch (error) {
      console.error('解析设备属性失败:', error);
    }
  },

  // 获取电池状态文本
  getBatteryStatus(properties: Record<string, any>): string {
    if (properties.ChargeStatus) {
      return '充电中';
    } else if (properties.DischargeStatus) {
      return '放电中';
    } else if (properties.DIStatus) {
      return '待机';
    } else {
      return '正常';
    }
  },

  // 蓝牙发送数据
  sendBluetoothMessage(messageData: any) {
    if (
      !this.data.isConnected ||
      !this.data.serviceId ||
      !this.data.characteristicId
    ) {
      wx.showToast({
        title: '设备未连接',
        icon: 'none',
      });
      return false;
    }

    try {
      // 将消息转换为字符串
      const messageString = JSON.stringify(messageData);
      console.log('发送蓝牙消息:', messageString);

      // 将字符串转换为ArrayBuffer
      const buffer = new ArrayBuffer(messageString.length);
      const uint8Array = new Uint8Array(buffer);
      for (let i = 0; i < messageString.length; i++) {
        uint8Array[i] = messageString.charCodeAt(i);
      }

      // 发送数据
      wx.writeBLECharacteristicValue({
        deviceId: this.data.deviceId,
        serviceId: this.data.serviceId,
        characteristicId: this.data.characteristicId,
        value: buffer,
        success: (res) => {
          console.log('蓝牙消息发送成功', res);
        },
        fail: (err) => {
          console.error('蓝牙消息发送失败', err);
          // wx.showToast({
          //   title: '发送失败',
          //   icon: 'none',
          // });
        },
      });

      return true;
    } catch (error) {
      console.error('蓝牙消息发送异常', error);
      wx.showToast({
        title: '发送异常',
        icon: 'none',
      });
      return false;
    }
  },

  // 强制启动
  onForceStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要强制启动设备吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '执行中...',
            mask: true,
          });

          const message = {
            dataType: '0x03',
            data: [
              {
                PutDJ: 'open',
              },
            ],
          };

          const success = this.sendBluetoothMessage(message);

          setTimeout(() => {
            wx.hideLoading();
            if (success) {
              wx.showToast({
                title: '强制启动命令已发送',
                icon: 'none',
              });
            }
          }, 1000);
        }
      },
    });
  },

  // 预约加热
  onScheduleHeat() {
    // 跳转到预约加热页面，传递蓝牙连接信息
    wx.navigateTo({
      url: `/pages/schedule-heat/schedule-heat?deviceId=${
        this.data.deviceId
      }&deviceName=${encodeURIComponent(
        this.data.deviceName
      )}&isBluetooth=true`,
    });
  },

  // 远程开机
  onRemoteStart() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程开机吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '执行中...',
            mask: true,
          });

          const message = {
            dataType: '0x03',
            data: [
              {
                PutCtrl: 'open',
              },
            ],
          };

          const success = this.sendBluetoothMessage(message);

          setTimeout(() => {
            wx.hideLoading();
            if (success) {
              wx.showToast({
                title: '远程开机命令已发送',
                icon: 'none',
              });
            }
          }, 1000);
        }
      },
    });
  },

  // 远程关机
  onRemoteShutdown() {
    wx.showModal({
      title: '确认操作',
      content: '确定要远程关机吗？此操作可能会影响设备正常运行。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '执行中...',
            mask: true,
          });

          const message = {
            dataType: '0x03',
            data: [
              {
                PutCtrl: 'close',
              },
            ],
          };

          const success = this.sendBluetoothMessage(message);

          setTimeout(() => {
            wx.hideLoading();
            if (success) {
              wx.showToast({
                title: '远程关机命令已发送',
                icon: 'none',
              });
            }
          }, 1000);
        }
      },
    });
  },

  // 断开连接
  disconnect() {
    if (!this.data.isConnected) {
      return;
    }

    wx.closeBLEConnection({
      deviceId: this.data.deviceId,
      success: (res) => {
        console.log('断开连接成功', res);
        this.onConnectionLost();
      },
      fail: (err) => {
        console.error('断开连接失败', err);
        // 即使断开失败，也更新状态
        this.onConnectionLost();
      },
    });
  },

  // 返回搜索页面
  goBack() {
    wx.navigateBack({
      fail: () => {
        wx.navigateTo({
          url: '/pages/bluetooth-search/bluetooth-search',
        });
      },
    });
  },

  // 开始连接时间计时器
  startConnectionTimer() {
    const timer = setInterval(() => {
      if (this.data.isConnected && this.data.connectStartTime > 0) {
        const duration = Date.now() - this.data.connectStartTime;
        const hours = Math.floor(duration / 3600000);
        const minutes = Math.floor((duration % 3600000) / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);

        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes
          .toString()
          .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        this.setData({ connectionTime: timeStr });
      }
    }, 1000);

    this.setData({ connectionTimer: timer });
  },

  // 开始数据率计时器
  startDataRateTimer() {
    const timer = setInterval(() => {
      const currentCount = this.data.receivedCount;
      const rate = currentCount - this.data.lastDataCount;

      this.setData({
        lastDataCount: currentCount,
      });
    }, 1000);

    this.setData({ dataRateTimer: timer });
  },

  // 停止所有计时器
  stopTimers() {
    if (this.data.connectionTimer) {
      clearInterval(this.data.connectionTimer);
      this.setData({ connectionTimer: null });
    }

    if (this.data.dataRateTimer) {
      clearInterval(this.data.dataRateTimer);
      this.setData({ dataRateTimer: null });
    }
  },

  // 清理资源
  cleanup() {
    this.stopTimers();

    // 断开连接
    if (this.data.isConnected) {
      wx.closeBLEConnection({
        deviceId: this.data.deviceId,
      });
    }
  },
});
