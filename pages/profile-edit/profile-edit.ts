import loginHelper from '../../common/loginHelper';
import { updateMe, createUser, getUserPhoneNumber } from '../../api/user';
import authService from '../../common/auth';
import storage from '../../common/storage';
import { eventBus } from '../../utils/eventBus';
import type { Code2SessionResponse } from '../../api/types';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      nickname: '',
      mobile: '',
    },
    originalUserInfo: {
      nickname: '',
      mobile: '',
    },
    isFromRegister: false, // 是否来自注册流程
    sessionData: null, // 注册时的session数据
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    console.log('个人信息编辑页面加载', options);

    // 检查是否来自注册流程
    if (options.from === 'register') {
      this.setData({ isFromRegister: true });
      // 获取session数据
      const sessionData = authService.getSessionData();
      if (sessionData) {
        this.setData({ sessionData });
      }
    }

    this.initUserInfo();
    this.setupEventListeners();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('个人信息编辑页面显示');
    // 每次页面显示时重新获取用户信息，确保显示最新数据
    if (!this.data.isFromRegister) {
      this.initUserInfo();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 移除事件监听
    this.removeEventListeners();
  },

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 监听用户信息更新事件
    eventBus.on('userInfoUpdate', this.onUserInfoUpdate.bind(this));
    eventBus.on('loginStatusUpdate', this.onLoginStatusUpdate.bind(this));
  },

  /**
   * 移除事件监听
   */
  removeEventListeners() {
    eventBus.off('userInfoUpdate', this.onUserInfoUpdate.bind(this));
    eventBus.off('loginStatusUpdate', this.onLoginStatusUpdate.bind(this));
  },

  /**
   * 用户信息更新事件处理
   */
  onUserInfoUpdate(data: any) {
    console.log('编辑页面收到用户信息更新事件:', data);
    if (data.userInfo && !this.data.isFromRegister) {
      const userInfo = {
        nickname: data.userInfo.nickname || '',
        mobile: data.userInfo.mobile || '',
      };

      this.setData({
        userInfo: { ...userInfo },
        originalUserInfo: { ...userInfo },
      });
    }
  },

  /**
   * 登录状态更新事件处理
   */
  onLoginStatusUpdate(data: any) {
    console.log('编辑页面收到登录状态更新事件:', data);
    if (data.isLoggedIn && data.userInfo && !this.data.isFromRegister) {
      const userInfo = {
        nickname: data.userInfo.nickname || '',
        mobile: data.userInfo.mobile || '',
      };

      this.setData({
        userInfo: { ...userInfo },
        originalUserInfo: { ...userInfo },
      });
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  /**
   * 初始化用户信息
   */
  async initUserInfo() {
    if (this.data.isFromRegister) {
      // 注册流程，不需要获取现有用户信息
      return;
    }

    try {
      // 获取当前用户信息
      const loginStatus = loginHelper.getLoginStatus();
      console.log('当前登录状态:', loginStatus);
      if (loginStatus.isLoggedIn && loginStatus.userInfo) {
        const userInfo = {
          nickname: loginStatus.userInfo.nickname || '',
          mobile: loginStatus.userInfo.mobile || '',
        };

        this.setData({
          userInfo: { ...userInfo },
          originalUserInfo: { ...userInfo },
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  /**
   * 用户名输入事件
   */
  onNicknameInput(e: any) {
    const value = e.detail.value;
    this.setData({
      'userInfo.nickname': value,
    });
  },

  /**
   * 获取微信手机号
   */
  async onGetPhoneNumber(e: any) {
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      wx.showToast({
        title: '获取手机号失败',
        icon: 'none',
      });
      return;
    }

    try {
      wx.showLoading({ title: '获取手机号中...' });

      const code = e.detail.code;
      console.log('code', code);
      const result = await getUserPhoneNumber({ code });
      console.log('result', result);
      if (result && result.data) {
        this.setData({
          'userInfo.mobile': result.data.phoneNumber,
        });
        const res = await updateMe({ mobile: result.data.phoneNumber });
        console.log('更新手机号结果:', res);
        // 同步本地用户信息
        if (res.code === 0) {
          storage.setUserInfo(res.data);
          loginHelper.refreshUserInfo();
        }

        wx.showToast({
          title: '获取手机号成功',
          icon: 'success',
        });
      } else {
        throw new Error(result.message || '获取手机号失败');
      }
    } catch (error) {
      console.error('获取手机号失败:', error);
      wx.showToast({
        title: '获取手机号失败',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 暂不填写按钮点击事件
   */
  onSkip() {
    console.log('暂不填写');
    if (this.data.isFromRegister) {
      // 注册流程跳过，返回首页
      wx.switchTab({
        url: '/pages/tabbar/index/index',
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 保存按钮点击事件
   */
  async onSave() {
    console.log('保存用户信息', this.data.userInfo);

    const { userInfo } = this.data;

    // 基本验证
    if (!userInfo.nickname.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none',
      });
      return;
    }

    if (this.data.loading) return;

    try {
      this.setData({ loading: true });
      wx.showLoading({ title: '保存中...' });

      if (this.data.isFromRegister) {
        // 注册流程 - 创建新用户
        await this.handleRegister();
      } else {
        // 编辑流程 - 更新用户信息
        await this.handleUpdate();
      }
    } catch (error) {
      console.error('保存失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none',
      });
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  /**
   * 处理用户注册
   */
  async handleRegister() {
    const { userInfo, sessionData } = this.data;

    if (!sessionData) {
      throw new Error('缺少注册信息');
    }

    // 创建用户
    const sessionInfo = sessionData as Code2SessionResponse;
    const createResult = await createUser({
      nickname: userInfo.nickname,
      mobile: userInfo.mobile,
      wx_openid: sessionInfo.openid,
      wx_unionid: sessionInfo.unionid || '',
      avatar: '',
    });

    if (createResult.code === 200 && createResult.data) {
      // 注册成功，更新登录状态
      const user = createResult.data;
      storage.setUserInfo(user);
      storage.setIsLogin(true);

      // 清除session数据
      storage.logout();

      wx.showToast({
        title: '注册成功',
        icon: 'success',
        duration: 1500,
      });

      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/tabbar/index/index',
        });
      }, 1500);
    } else {
      throw new Error(createResult.message || '注册失败');
    }
  },

  /**
   * 处理用户信息更新
   */
  async handleUpdate() {
    const { userInfo, originalUserInfo } = this.data;

    // 检查是否有变更
    const hasChanges =
      userInfo.nickname !== originalUserInfo.nickname ||
      userInfo.mobile !== originalUserInfo.mobile;

    if (!hasChanges) {
      wx.showToast({
        title: '没有修改内容',
        icon: 'none',
      });
      return;
    }

    // 更新用户信息
    const updateData: any = {};
    if (userInfo.nickname !== originalUserInfo.nickname) {
      updateData.nickname = userInfo.nickname;
    }
    if (userInfo.mobile !== originalUserInfo.mobile) {
      updateData.mobile = userInfo.mobile;
    }

    const updateResult = await updateMe(updateData);

    if (updateResult.code === 0) {
      // 刷新用户信息
      await loginHelper.refreshUserInfo();

      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1500,
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } else {
      throw new Error(updateResult.message || '更新失败');
    }
  },
});
