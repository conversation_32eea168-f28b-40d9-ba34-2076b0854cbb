/* pages/profile-edit/profile-edit.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.page-container {
  flex: 1;
  overflow-y: hidden;
}

.content {
  flex: 1;
}

.form-section {
  padding: 0;
}

.form-item {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  padding: 0 0 0 16px;
  &:first-child {
    border-top: 0.5px solid #e7e7e7;
  }
  border-bottom: 0.5px solid #e7e7e7;

  .form-row {
    display: flex;
    gap: 16px;
    height: 56px;
    align-items: center;
  }

  .form-label {
    width: 81px;
    flex-shrink: 0;

    text {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.9);
    }
  }

  .form-input {
    flex: 1;

    input {
      width: 100%;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.9);
      background: transparent;
      border: none;
      outline: none;
    }

    .input-placeholder {
      color: rgba(0, 0, 0, 0.4);
    }

    &.phone-section {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .phone-display {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;

        .phone-number {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          color: rgba(0, 0, 0, 0.9);
          flex: 1;
        }

        .rebind-btn {
          background-color: #001a57;
          color: white;
          border: none;
          border-radius: 16px;
          padding: 4px 12px;
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          margin-right: 30rpx;

          &:after {
            border: none;
          }

          &:active {
            background-color: #001440;
          }
        }
      }

      .get-phone-btn {
        background-color: #001a57;
        color: white;
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin-right: 16px;

        &:after {
          border: none;
        }

        &:active {
          background-color: #001440;
        }
      }
    }
  }
}

.button-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

.button-container {
  display: flex;
  gap: 16px;
  align-items: stretch;
}

.button-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding: 12px 20px;
  border-radius: 100px;
  cursor: pointer;
  transition: all 0.2s ease;

  text {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
  }

  &.secondary {
    background-color: #ffffff;
    border: 1px solid var(--primaryColor);

    text {
      color: var(--primaryColor);
    }

    &:active {
      background-color: rgba(0, 82, 217, 0.05);
    }
  }

  &.primary {
    background-color: var(--primaryColor);
    border: 1px solid var(--primaryColor);

    text {
      color: #ffffff;
    }

    &:active {
      // background-color: rgba(0, 82, 217, 0.05);
    }
  }
}

.safe-area-bottom {
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
}
