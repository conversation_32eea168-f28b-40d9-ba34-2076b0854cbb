<!--pages/profile-edit/profile-edit.wxml-->
<t-navbar title="个人信息" left-arrow />

<scroll-view class="page-container" scroll-y type="list">
  <view class="content">
    <!-- 输入表单区域 -->
    <view class="form-section">
      <!-- 用户名输入 -->
      <view class="form-item">
        <view class="form-row">
          <view class="form-label">
            <text>用户名</text>
          </view>
          <view class="form-input">
            <input type="text" placeholder="请输入用户名" value="{{userInfo.nickname}}" bindinput="onNicknameInput"
              placeholder-class="input-placeholder" />
          </view>
        </view>
      </view>

      <!-- 联系方式获取 -->
      <view class="form-item">
        <view class="form-row">
          <view class="form-label">
            <text>联系方式</text>
          </view>
          <view class="form-input phone-section">
            <view class="phone-display" wx:if="{{userInfo.mobile}}">
              <text class="phone-number">{{userInfo.mobile}}</text>
              <button class="rebind-btn" size="mini" bindgetphonenumber="onGetPhoneNumber" open-type="getPhoneNumber">
                重新绑定
              </button>
            </view>
            <button class="get-phone-btn" wx:else bindgetphonenumber="onGetPhoneNumber" open-type="getPhoneNumber">
              绑定手机号
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮区域 -->
  <view class="button-section">
    <view class="button-container">
      <view class="button-item secondary" bindtap="onSkip">
        <text>暂不填写</text>
      </view>
      <view class="button-item primary" bindtap="onSave">
        <text>保存</text>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</scroll-view>