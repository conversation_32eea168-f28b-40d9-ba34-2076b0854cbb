/**
 * 根据运行环境获取配置文件
 * 默认为生产模式
 */

const development = require('./development');
const production = require('./production');

// 根据不同环境，来这里切换
// 修改默认环境为开发环境
let env: string = 'development';

/**
 * 设置环境变量
 */
function setENV(param?: string): any {
  env = param || 'development'; // 修改默认值为development
  // 重新导出当前环境的配置
  const newConfig = getConfig();
  Object.entries(newConfig).forEach(([key, value]) => {
    (module.exports as any)[key] = value;
  });
  return newConfig;
}

/**
 * 获取环境变量
 */
function getENV(): string {
  return env;
}

/**
 * 导出配置对象
 */
function getConfig(): any {
  let config = production;
  if (env === 'development') {
    config = development;
  } else {
    config = production;
  }
  return config;
}

// 获取当前环境的配置
const currentConfig = getConfig();

// 将当前配置的所有属性导出
Object.entries(currentConfig).forEach(([key, value]) => {
  (module.exports as any)[key] = value;
});

// CommonJS 导出
module.exports = {
  ...currentConfig,
  setENV,
  getENV,
  getConfig,
};

// ES 模块导出（为了兼容性）
export { setENV, getENV, getConfig };
export default module.exports;
