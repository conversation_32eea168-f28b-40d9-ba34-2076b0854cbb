/// <reference path="./types/index.d.ts" />

interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo | any | null;
    eventBus?: any; // 事件总线
    isLoggedIn?: boolean; // 登录状态
  };
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback;

  // 自动登录相关方法
  performAutoLogin?(): Promise<void>;
  updateLoginStatus?(): void;
  login?(): Promise<boolean>;
  logout?(): void;
  getLoginStatus?(): { isLoggedIn: boolean; userInfo: any };
  refreshUserInfo?(): Promise<boolean>;
}
