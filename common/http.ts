import config from '../config/index';

export default class Http {
  private header: Record<string, string>;

  constructor(isForm?: boolean) {
    // 设置默认的http头信息
    this.header = isForm
      ? {
          'content-type': 'application/x-www-form-urlencoded',
          'Accept-Language': 'zh-CN',
          'X-WXMA-APPID': config.appId,
        }
      : {
          'content-type': 'application/json',
          'Accept-Language': 'zh-CN',
          'X-WXMA-APPID': config.appId,
        };
  }

  /**
   * 设置header信息
   */
  setHeader(header: Record<string, string> | null): this {
    if (header) {
      this.header = { ...this.header, ...header };
    }
    return this;
  }

  doGet(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'GET',
        header: this.header,
        success: function (res) {
          resolve(res);
        },
        fail: function (err) {
          // wx.showToast({ title: '请求异常，请稍后重试', icon: 'none' });
          reject(err);
        },
      });
    });
  }

  doPost(url: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'POST',
        data: data,
        header: this.header,
        success: function (res) {
          resolve(res);
        },
        fail: function (err) {
          // wx.showToast({ title: '请求异常，请稍后重试', icon: 'none' });
          reject(err);
        },
      });
    });
  }

  doPut(url: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'PUT',
        data: data,
        header: this.header,
        success: function (res) {
          resolve(res);
        },
        fail: function (err) {
          // wx.showToast({ title: '请求异常，请稍后重试', icon: 'none' });
          reject(err);
        },
      });
    });
  }

  doDelete(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'DELETE',
        header: this.header,
        success: function (res) {
          resolve(res);
        },
        fail: function (err) {
          // wx.showToast({ title: '请求异常，请稍后重试', icon: 'none' });
          reject(err);
        },
      });
    });
  }

  doPatch(url: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'PATCH' as any,
        data: data,
        header: this.header,
        success: function (res) {
          resolve(res);
        },
        fail: function (err) {
          // wx.showToast({ title: '请求异常，请稍后重试', icon: 'none' });
          reject(err);
        },
      });
    });
  }

  doUploadFile(
    url: string,
    filePath: string,
    name?: string,
    formData?: any
  ): Promise<any> {
    console.log(url);
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: url,
        filePath: filePath,
        name: name || 'file',
        formData: formData || {
          image: 0,
        },
        header: this.header,
        success: function (res) {
          resolve(res);
        },
        fail: function (err) {
          // wx.showToast({ title: '请求异常，请稍后重试', icon: 'none' });
          reject(err);
        },
      });
    });
  }
}
