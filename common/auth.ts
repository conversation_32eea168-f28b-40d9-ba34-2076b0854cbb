import { login } from '../api/auth';
import { getMe } from '../api/user';
import storage from './storage';
import type { AccessToken, Code2SessionResponse } from '../api/types';

/**
 * 认证服务类
 * 处理自动登录、token管理等认证相关功能
 */
class AuthService {
  /**
   * 自动登录
   * 在小程序启动时调用，处理完整的登录流程
   */
  async autoLogin(): Promise<boolean> {
    try {
      console.log('开始自动登录...');

      // 1. 检查是否已有有效的token
      const existingToken = storage.getAccessToken();
      if (existingToken && existingToken.isValid()) {
        console.log('发现有效token，验证用户信息...');

        // 验证token是否真的有效（通过调用用户信息接口）
        try {
          const userResult = await getMe();
          if (userResult && userResult.data) {
            console.log('token验证成功，用户已登录');
            storage.setUserInfo(userResult.data);
            storage.setIsLogin(true);
            return true;
          }
        } catch (error) {
          console.log('token验证失败，需要重新登录');
          // token无效，清除存储的认证信息
          this.clearAuthData();
        }
      }

      // 2. 没有有效token，进行微信登录
      console.log('开始微信登录...');
      const wxLoginResult = await this.wxLogin();
      if (!wxLoginResult.code) {
        throw new Error('微信登录失败');
      }

      // 3. 调用登录接口
      console.log('调用后端登录接口...');
      const loginResult = await login({ code: wxLoginResult.code });
      console.log('后端登录接口返回:', loginResult);

      if (!loginResult || !loginResult.data) {
        throw new Error('后端登录接口调用失败');
      }

      // 4. 处理登录结果
      const loginData = loginResult.data;

      // 判断返回的是token还是需要创建用户
      if (this.isAccessToken(loginData)) {
        // 返回的是AccessToken，登录成功
        console.log('登录成功，保存token...', loginData);
        this.saveTokenData(loginData);

        // 获取用户信息
        const userResult = await getMe();
        console.log('获取用户信息成功:', userResult);
        if (userResult) {
          storage.setUserInfo(userResult);
          storage.setIsLogin(true);
          console.log('自动登录完成');
          return true;
        } else {
          throw new Error('获取用户信息失败');
        }
      } else {
        // 返回的是Code2SessionResponse，需要创建用户
        console.log('用户未注册，需要创建用户');
        this.saveSessionData(loginData as Code2SessionResponse);
        storage.setIsLogin(false);
        return false; // 需要引导用户完成注册
      }
    } catch (error) {
      console.error('自动登录失败:', error);
      // this.clearAuthData();
      // storage.setIsLogin(false);
      return false;
    }
  }

  /**
   * 微信登录获取code
   */
  private wxLogin(): Promise<WechatMiniprogram.LoginSuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('微信登录成功，code:', res.code);
            storage.setCode(res.code);
            resolve(res);
          } else {
            reject(new Error('微信登录失败'));
          }
        },
        fail: (error) => {
          reject(error);
        },
      });
    });
  }

  /**
   * 判断登录返回数据是否为AccessToken
   */
  private isAccessToken(data: any): data is AccessToken {
    return (
      data &&
      typeof data.accessToken === 'string' &&
      typeof data.expireAt === 'number'
    );
  }

  /**
   * 保存token数据
   */
  private saveTokenData(tokenData: AccessToken): void {
    // 计算过期时间（转换为毫秒时间戳）
    const expiresIn = tokenData.expireAt - Math.floor(Date.now() / 1000);
    storage.saveAccessToken(tokenData.accessToken, expiresIn);
    console.log('token已保存，过期时间:', new Date(tokenData.expireAt * 1000));
  }

  /**
   * 保存会话数据
   */
  private saveSessionData(sessionData: Code2SessionResponse): void {
    storage.setSession({
      openid: sessionData.openid,
      unionid: sessionData.unionid,
      timestamp: Date.now(),
    });
    console.log('会话数据已保存');
  }

  /**
   * 清除认证相关数据
   */
  private clearAuthData(): void {
    storage.logout();
    console.log('认证数据已清除');
  }

  /**
   * 获取当前登录状态
   */
  isLoggedIn(): boolean {
    const isLogin = storage.getIsLogin();
    const token = storage.getAccessToken();
    return Boolean(isLogin && token && token.isValid());
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): any {
    return storage.getUserInfo();
  }

  /**
   * 登出
   */
  logout(): void {
    this.clearAuthData();
    console.log('用户已登出');
  }

  /**
   * 获取存储的会话数据（用于用户注册）
   */
  getSessionData(): any {
    return storage.getSession();
  }

  /**
   * 手动刷新用户信息
   */
  async refreshUserInfo(): Promise<boolean> {
    try {
      console.log('尝试刷新用户信息...');
      if (!this.isLoggedIn()) {
        return false;
      }

      const userResult = await getMe();
      console.log('刷新用户信息成功:', userResult);
      if (userResult) {
        storage.setUserInfo(userResult);
        return true;
      }
      return false;
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      return false;
    }
  }
}

// 创建单例
const authService = new AuthService();

export default authService;
