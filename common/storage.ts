import Token from '../common/token';

/**
 * local storage 存储的键常量定义
 *
 */

const KEY_LOGIN = 'isLogin';
const KEY_SESSION = 'session';
const KEY_USERINFO = 'user';
const KEY_ACCESS_TOKEN = 'accessToken';
const KEY_REFRESH_TOKEN = 'refreshToken';
const KEY_CODE = 'code';
const KEY_AVATAR = 'avatar';
const KEY_NICKNAME = 'nickname';

/**
 * 本地缓存信息
 * 主要是调用小程序storage来缓存到本地
 * 注意：小程序的storage有同步和异步的操作方式，我们统一采用同步的方式，虽然同步的方式会有一点性能问题，从使用场景来看应该是没有问题的
 */
class StorageService {
  setIsLogin(login: boolean): void {
    wx.setStorageSync(KEY_LOGIN, login);
  }

  getIsLogin(): boolean | null {
    try {
      return wx.getStorageSync(KEY_LOGIN);
    } catch (e) {
      return null;
    }
  }

  setSession(session: any): void {
    wx.setStorageSync(KEY_SESSION, session);
  }

  getSession(): any {
    try {
      return wx.getStorageSync(KEY_SESSION);
    } catch (e) {
      return null;
    }
  }

  getSessionAsync(): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.getStorage({
        key: KEY_SESSION,
        success(res) {
          resolve(res.data);
        },
        fail(err) {
          reject(err);
        },
      });
    });
  }

  getToken(): string {
    try {
      let accessToken = this.getAccessToken();
      if (accessToken && !accessToken.isValid()) return '';
      return accessToken ? accessToken.token : '';
    } catch (e) {
      return '';
    }
  }

  setUserInfo(user: any): void {
    wx.setStorageSync(KEY_USERINFO, user);
  }

  getUserInfo(): any {
    try {
      return wx.getStorageSync(KEY_USERINFO);
    } catch (e) {
      return null;
    }
  }

  removeUserInfo(): void {
    wx.removeStorageSync(KEY_USERINFO);
  }

  setCode(code: string): void {
    wx.setStorageSync(KEY_CODE, code);
  }

  getCode(): string {
    try {
      return wx.getStorageSync(KEY_CODE);
    } catch (e) {
      return '';
    }
  }

  removeCode(): void {
    wx.removeStorageSync(KEY_CODE);
  }

  setAvatar(avatar: string): void {
    wx.setStorageSync(KEY_AVATAR, avatar);
  }

  getAvatar(): string {
    try {
      return wx.getStorageSync(KEY_AVATAR);
    } catch (e) {
      return '';
    }
  }

  setNickname(nickname: string): void {
    wx.setStorageSync(KEY_NICKNAME, nickname);
  }

  getNickname(): string {
    try {
      return wx.getStorageSync(KEY_NICKNAME);
    } catch (e) {
      return '';
    }
  }

  /**
   * 获取access token对象
   */
  getAccessToken(): Token | null {
    try {
      const json = wx.getStorageSync(KEY_ACCESS_TOKEN);
      if (json) {
        return new Token(json.token, json.expireTime);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /**
   * 保存access token对象
   * 过期时间，因网络延迟等，将实际过期时间提前60秒，以防止临界点
   * @param {*} access_token
   * @param {*} expires_in 过期时间，秒
   */
  saveAccessToken(access_token: string, expires_in: number): Token {
    const expireTime =
      Date.now() + (expires_in > 0 ? expires_in - 60 : 7200 - 60) * 1000;
    // const expireTime = Date.now() + (expires_in > 0 ? (120 - 60) : (120-60)) * 1000;
    const token = new Token(access_token, expireTime);
    wx.setStorageSync(KEY_ACCESS_TOKEN, token);
    return token;
  }

  /**
   * 获取refresh token对象
   */
  getRefreshToken(): Token | null {
    try {
      const json = wx.getStorageSync(KEY_REFRESH_TOKEN);
      if (json) {
        return new Token(json.token, json.expireTime);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /**
   * 保存refresh token对象
   * 过期时间，将实际过期时间提前60秒，以防止临界点
   * 默认一个星期过期
   * @param {*} fresh_token
   */
  saveRefreshToken(fresh_token: string): Token {
    const expireTime = Date.now() + (7 * 24 * 60 * 60 - 60) * 1000;
    const token = new Token(fresh_token, expireTime);
    wx.setStorageSync(KEY_REFRESH_TOKEN, token);
    return token;
  }

  clear(): void {
    wx.clearStorageSync();
  }

  logout(): void {
    wx.removeStorageSync(KEY_SESSION);
    wx.removeStorageSync(KEY_ACCESS_TOKEN);
    wx.removeStorageSync(KEY_REFRESH_TOKEN);
    wx.removeStorageSync(KEY_LOGIN);
    wx.removeStorageSync(KEY_CODE);
    wx.removeStorageSync(KEY_USERINFO);
  }
}

export default new StorageService();
