import authService from './auth';
import { eventBus } from '../utils/eventBus';
import { createUser, getUserPhoneNumber } from '../api/user';
import type { CreateUserRequest } from '../api/types';

/**
 * 登录辅助工具类
 * 提供页面中使用的登录相关方法
 */
class LoginHelper {
  /**
   * 页面初始化时检查登录状态
   * @param page 页面实例
   * @param options 配置选项
   */
  initPageLogin(
    page: any,
    options: {
      requireLogin?: boolean; // 是否需要登录
      redirectUrl?: string; // 未登录时的跳转地址
      onLoginSuccess?: (userInfo: any) => void; // 登录成功回调
      onLoginFailed?: () => void; // 登录失败回调
      onNeedRegister?: (sessionData: any) => void; // 需要注册回调
    } = {}
  ) {
    const app = getApp<IAppOption>();

    // 检查当前登录状态
    const loginStatus = app.getLoginStatus?.();
    if (loginStatus?.isLoggedIn) {
      // 已登录
      options.onLoginSuccess?.(loginStatus.userInfo);
      return true;
    }

    // 未登录，根据配置处理
    if (options.requireLogin) {
      this.handleRequireLogin(options.redirectUrl);
      return false;
    }

    // 监听登录相关事件
    this.setupLoginEvents(page, options);
    return false;
  }

  /**
   * 设置登录相关事件监听
   */
  private setupLoginEvents(page: any, options: any) {
    // 登录成功事件
    const onLoginSuccess = (data: any) => {
      console.log('页面收到登录成功事件:', data);
      options.onLoginSuccess?.(data.userInfo);
    };

    // 登录失败事件
    const onLoginFailed = () => {
      console.log('页面收到登录失败事件');
      options.onLoginFailed?.();
    };

    // 需要注册事件
    const onNeedRegister = (data: any) => {
      console.log('页面收到需要注册事件:', data);
      options.onNeedRegister?.(data.sessionData);
    };

    // 登录状态更新事件
    const onLoginStatusUpdate = (data: any) => {
      console.log('页面收到登录状态更新事件:', data);
      if (data.isLoggedIn) {
        options.onLoginSuccess?.(data.userInfo);
      } else {
        options.onLoginFailed?.();
      }
    };

    // 绑定事件
    eventBus.on('loginSuccess', onLoginSuccess);
    eventBus.on('loginFailed', onLoginFailed);
    eventBus.on('needRegister', onNeedRegister);
    eventBus.on('loginStatusUpdate', onLoginStatusUpdate);

    // 页面卸载时移除事件监听
    const originalOnUnload = page.onUnload || function () {};
    page.onUnload = function () {
      eventBus.off('loginSuccess', onLoginSuccess);
      eventBus.off('loginFailed', onLoginFailed);
      eventBus.off('needRegister', onNeedRegister);
      eventBus.off('loginStatusUpdate', onLoginStatusUpdate);
      originalOnUnload.call(this);
    };
  }

  /**
   * 处理需要登录的情况
   */
  private handleRequireLogin(redirectUrl?: string) {
    const url = redirectUrl || '/pages/login/login';

    wx.showModal({
      title: '提示',
      content: '请先登录',
      showCancel: false,
      success: () => {
        wx.redirectTo({
          url: url,
          fail: () => {
            wx.navigateTo({ url });
          },
        });
      },
    });
  }

  /**
   * 手动触发登录
   */
  async manualLogin(): Promise<boolean> {
    try {
      wx.showLoading({ title: '登录中...' });

      const app = getApp<IAppOption>();
      const success = await app.login?.();

      if (success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success',
        });
        return true;
      } else {
        wx.showToast({
          title: '登录失败',
          icon: 'none',
        });
        return false;
      }
    } catch (error) {
      console.error('手动登录失败:', error);
      wx.showToast({
        title: '登录出错',
        icon: 'none',
      });
      return false;
    } finally {
      wx.hideLoading();
    }
  }

  /**
   * 获取微信用户手机号
   * @param code 手机号获取凭证
   * @returns Promise<string | null> 返回手机号或null
   */
  async getWxPhoneNumber(code: string): Promise<string | null> {
    try {
      const result = await getUserPhoneNumber({ code });
      if (result && result.data) {
        return result.data.phoneNumber;
      }
      return null;
    } catch (error) {
      console.error('获取手机号失败:', error);
      wx.showToast({
        title: '获取手机号失败',
        icon: 'none',
      });
      return null;
    }
  }

  /**
   * 用户注册
   */
  async registerUser(userInfo: {
    nickname: string;
    avatar: string;
    mobile: string;
  }): Promise<boolean> {
    try {
      wx.showLoading({ title: '注册中...' });

      // 获取会话数据
      const sessionData = authService.getSessionData();
      if (!sessionData || !sessionData.openid) {
        throw new Error('会话数据无效，请重新登录');
      }

      // 准备注册数据
      const userData: CreateUserRequest = {
        wx_openid: sessionData.openid,
        wx_unionid: sessionData.unionid,
        nickname: userInfo.nickname,
        avatar: userInfo.avatar,
        mobile: userInfo.mobile,
      };

      // 调用注册接口
      const result = await createUser(userData);
      if (result && result.data) {
        // 注册成功，重新登录
        const loginSuccess = await this.manualLogin();
        if (loginSuccess) {
          wx.showToast({
            title: '注册成功',
            icon: 'success',
          });
          return true;
        }
      }

      throw new Error('注册失败');
    } catch (error) {
      console.error('用户注册失败:', error);
      wx.showToast({
        title: error instanceof Error ? error.message : '注册失败',
        icon: 'none',
      });
      return false;
    } finally {
      wx.hideLoading();
    }
  }

  /**
   * 快速注册（使用微信手机号）
   * @param userProfile 用户基本信息（昵称、头像）
   * @param phoneCode 手机号获取凭证
   * @returns Promise<boolean>
   */
  async quickRegister(
    userProfile: {
      nickname: string;
      avatar: string;
    },
    phoneCode: string
  ): Promise<boolean> {
    try {
      wx.showLoading({ title: '注册中...' });

      // 获取手机号
      const phoneNumber = await this.getWxPhoneNumber(phoneCode);
      if (!phoneNumber) {
        throw new Error('获取手机号失败');
      }

      // 执行注册
      return await this.registerUser({
        nickname: userProfile.nickname,
        avatar: userProfile.avatar,
        mobile: phoneNumber,
      });
    } catch (error) {
      console.error('快速注册失败:', error);
      wx.showToast({
        title: error instanceof Error ? error.message : '注册失败',
        icon: 'none',
      });
      return false;
    } finally {
      wx.hideLoading();
    }
  }

  /**
   * 登出
   */
  logout() {
    const app = getApp<IAppOption>();
    app.logout?.();

    wx.showToast({
      title: '已登出',
      icon: 'success',
    });
  }

  /**
   * 获取当前登录状态
   */
  getLoginStatus() {
    const app = getApp<IAppOption>();
    return app.getLoginStatus?.() || { isLoggedIn: false, userInfo: null };
  }

  /**
   * 刷新用户信息
   */
  async refreshUserInfo(): Promise<boolean> {
    try {
      const app = getApp<IAppOption>();
      const success = await app.refreshUserInfo?.();
      return success || false;
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      return false;
    }
  }

  /**
   * 检查登录状态并执行回调
   */
  checkLoginAndExecute(
    callback: () => void,
    requireLoginCallback?: () => void
  ) {
    const loginStatus = this.getLoginStatus();
    if (loginStatus.isLoggedIn) {
      callback();
    } else {
      requireLoginCallback?.() || this.handleRequireLogin();
    }
  }
}

// 创建单例
const loginHelper = new LoginHelper();

export default loginHelper;
