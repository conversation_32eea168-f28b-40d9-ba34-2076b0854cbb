.custom-tab-bar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.custom-tab-bar-wrapper .text {
  font-size: 24rpx;
  margin-top: 4rpx;
  transition: color 0.2s ease-in-out;
}

/* 图标过渡动画 */
.custom-tab-bar-wrapper t-icon {
  transition: transform 0.15s ease-in-out, opacity 0.1s ease-in-out;
}

/* 激活状态的动画效果 */
.custom-tab-bar-wrapper.active t-icon {
  transform: scale(1.1);
}

/* 防止闪烁 */
.t-tab-bar {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
