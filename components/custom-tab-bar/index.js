import TabMenu from './data';

Component({
  data: {
    active: 0,
    list: TabMenu,
  },

  attached() {
    this.init();
  },

  methods: {
    onChange(event) {
      const index = event.detail.value;
      const url = this.data.list[index].url;

      // 立即更新UI状态，提供即时视觉反馈
      this.setData({ active: index });

      // 页面切换
      wx.switchTab({
        url: url.startsWith('/') ? url : `/${url}`,
        success: () => {
          // 切换成功后确保状态正确
          this.setData({ active: index });
        },
        fail: (err) => {
          console.error('Tab切换失败:', err);
          // 切换失败时恢复之前的状态
          this.init();
        },
      });
    },

    init() {
      const page = getCurrentPages().pop();
      if (!page) return;

      const route = page.route.split('?')[0];
      const active = this.data.list.findIndex((item) => {
        const itemUrl = item.url.startsWith('/')
          ? item.url.substr(1)
          : item.url;
        return itemUrl === route;
      });

      if (active !== -1 && active !== this.data.active) {
        this.setData({ active });
      }
    },

    // 提供外部调用的方法来更新活跃状态
    updateActiveTab(index) {
      if (index >= 0 && index < this.data.list.length) {
        this.setData({ active: index });
      }
    },
  },
});
