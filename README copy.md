# BMS 微信小程序 - 设备信息页面

## 项目概述

这是一个基于微信小程序的 BMS（电池管理系统）设备监控应用，主要功能是实时显示设备状态、电池信息和提供远程控制能力。

## 设备信息页面功能

### 🔋 设备状态监控

- **实时电池电量显示**：86%电量的可视化展示
- **设备状态指示**：在线/离线状态，正常/异常状态
- **报警信息**：总体过压报警等关键信息提醒

### 📊 关键参数展示

- **电压监控**：实时显示设备电压值
- **电流监控**：实时显示设备电流值
- **功率监控**：实时显示设备功率值

### 🎛️ 远程控制功能

- **强制启动**：紧急情况下强制启动设备
- **预约加热**：支持定时加热功能
- **远程开机**：远程启动设备
- **远程关机**：远程关闭设备

### 📍 位置服务

- **设备位置**：显示设备实时位置
- **地图展示**：集成地图组件展示设备位置
- **位置导航**：支持跳转到系统地图进行导航

### 🔄 数据刷新

- **下拉刷新**：支持下拉刷新获取最新数据
- **自动更新**：模拟实时数据更新

## 技术特点

### 📱 响应式设计

- 严格按照 Figma 设计稿还原
- 支持不同屏幕尺寸适配
- 针对小屏设备优化布局

### 🎨 UI 设计

- **颜色规范**：严格遵循设计稿的颜色标准
- **间距控制**：精确还原设计稿的间距规范
- **动画效果**：电池充电动画、按钮点击反馈
- **现代化界面**：毛玻璃效果、渐变色彩

### 🔧 技术栈

- **框架**：微信小程序原生框架
- **UI 组件库**：TDesign 微信小程序组件库
- **样式预处理**：Less
- **开发语言**：TypeScript
- **地图服务**：微信原生地图组件

## 使用说明

### 权限要求

- **位置权限**：获取设备位置信息
- **网络权限**：获取设备数据和执行远程控制

### 操作指南

1. **查看设备状态**：页面加载后自动显示设备实时状态
2. **刷新数据**：下拉页面可刷新最新数据
3. **远程控制**：点击相应按钮执行设备控制操作
4. **查看位置**：点击地图区域可打开系统地图导航
5. **页面切换**：底部标签栏切换不同功能页面

### 安全提醒

- 远程关机操作需要确认，避免误操作
- 强制启动仅在紧急情况下使用
- 所有远程操作都有确认提示

## 开发指南

### 本地开发

```bash
# 安装依赖
npm install

# 使用微信开发者工具打开项目
# 导入项目根目录
```

### 项目结构

```
pages/index/
├── index.wxml     # 页面结构
├── index.less     # 页面样式
├── index.ts       # 页面逻辑
└── index.json     # 页面配置
```

### 自定义配置

在 `pages/index/index.ts` 中可以修改：

- 设备信息数据
- 地图位置坐标
- 数据刷新频率
- 操作确认提示

## 设计规范

### 颜色规范

- 主色调：`#001a57` (蓝色)
- 警告色：`#E37318` (橙色)
- 成功色：`#07D943` (绿色)
- 背景色：`#F5F5F5` (浅灰)
- 文字色：`rgba(0, 0, 0, 0.9)` (深黑)

### 间距规范

- 页面边距：48rpx
- 组件间距：24rpx - 48rpx
- 内容间距：8rpx - 16rpx

### 字体规范

- 标题：PingFang SC Medium 18px
- 正文：PingFang SC Regular 14px
- 数值：PingFang SC Semibold 18px

## 更新日志

### v1.0.0 (2024-12-XX)

- ✨ 实现设备信息页面基础功能
- 🎨 完成 Figma 设计稿的精确还原
- 📱 适配不同屏幕尺寸
- 🔋 实现电池状态可视化
- 🗺️ 集成地图位置服务
- 🎛️ 实现远程控制功能

## 许可证

MIT License
