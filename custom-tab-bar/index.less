.custom-tab-bar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  height: 100%;
  min-height: 80rpx;
  padding: 8rpx 0;
}

/* 普通tabbar项目的容器 */
.custom-tab-bar-wrapper:not(.add-button) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
}

.custom-tab-bar-wrapper .text {
  font-size: 24rpx;
  margin-top: 0;
  text-align: center;
  line-height: 1.2;
  /* transition: color 0.2s ease-in-out; */
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 图标样式 */
.custom-tab-bar-wrapper .tab-icon {
  width: 36rpx;
  height: 36rpx;
  transition: transform 0.15s ease-in-out, opacity 0.1s ease-in-out;
  margin-bottom: 6rpx;
}

/* 激活状态的动画效果 */
.custom-tab-bar-wrapper.active .tab-icon {
  transform: scale(1.1);
}

/* 防止闪烁 */
.t-tab-bar {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.wrapper {
  --td-tab-bar-active-color: var(--td-brand-color, #fff);
  --td-tab-bar-active-bg: var(--td-bg-color-secondarycontainer, #001a57);
  --td-button-primary-bg-color: #001a57;
  --td-button-primary-border-color: #001a57;
  --td-button-primary-color: #ffffff;
  --td-brand-color: #ffffff;
  position: relative;
}

/* 为突出的加号按钮预留空间 */
.wrapper .t-tab-bar {
  position: relative;
  overflow: visible;
}

/* 加号按钮样式 */
.custom-tab-bar-wrapper.add-button {
  position: relative;
  justify-content: flex-end;
  padding-bottom: 0;
}

.add-button-container {
  position: absolute;
  top: -50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 92rpx;
  height: 92rpx;
  background: #f2f3ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* box-shadow: 0 6rpx 16rpx rgba(0, 26, 87, 0.3); */
  /* border: 6rpx solid #ffffff; */
  z-index: 10;
}

.add-icon {
  width: 72rpx;
  height: 72rpx;
  /* filter: brightness(0) invert(1); */
}

/* 加号按钮点击效果已移除，避免交互错位 */

/* 扫码确认弹窗样式 */
.scan-confirm-popup {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 0 32rpx 0;
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.popup-title {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 36rpx;
  line-height: 52rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

.device-info-section {
  padding: 0 48rpx;
  margin-bottom: 32rpx;
}

.device-info-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx 0;
}

.device-info-label {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 28rpx;
  line-height: 36rpx;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
}

.device-info-divider {
  flex: 1;
  height: 0;
  border-top: 2rpx dashed #e7e7e7;
}

.device-info-value {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 28rpx;
  line-height: 32rpx;
  color: rgba(0, 0, 0, 0.9);
  white-space: nowrap;
}

.popup-buttons {
  display: flex;
  gap: 60rpx;
  padding: 0 48rpx;
}

.popup-btn {
  flex: 1;
}

/* 自定义按钮样式 */
.custom-cancel-btn,
.custom-confirm-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.custom-cancel-btn {
  background-color: #ffffff;
  border: 2rpx solid #001a57;
  color: #001a57;
}

.custom-cancel-btn:active {
  background-color: rgba(0, 26, 87, 0.05);
}

.custom-confirm-btn {
  background-color: #001a57;
  border: 2rpx solid #001a57;
  color: #ffffff;
}

.custom-confirm-btn:active {
  background-color: rgba(0, 26, 87, 0.8);
}

/* 使用更高优先级的选择器覆盖TDesign按钮样式 */

/* 取消按钮 - outline variant */
.scan-confirm-popup .popup-buttons .cancel-btn {
  border-color: #001a57 !important;
  color: #001a57 !important;
}

.scan-confirm-popup .popup-buttons .cancel-btn.t-button {
  border-color: #001a57 !important;
  color: #001a57 !important;
}

.scan-confirm-popup .popup-buttons .cancel-btn.t-button::after {
  border-color: #001a57 !important;
}

/* 确认按钮 - base variant */
.scan-confirm-popup .popup-buttons .confirm-btn {
  background-color: #001a57 !important;
  border-color: #001a57 !important;
  color: #ffffff !important;
}

.scan-confirm-popup .popup-buttons .confirm-btn.t-button {
  background-color: #001a57 !important;
  border-color: #001a57 !important;
  color: #ffffff !important;
}

.scan-confirm-popup .popup-buttons .confirm-btn.t-button::after {
  background-color: #001a57 !important;
  border-color: #001a57 !important;
}

/* 使用深度选择器强制覆盖 */
.scan-confirm-popup ::deep .t-button--outline.t-button--primary {
  border-color: #001a57 !important;
  color: #001a57 !important;
}

.scan-confirm-popup ::deep .t-button--base.t-button--primary {
  background-color: #001a57 !important;
  border-color: #001a57 !important;
  color: #ffffff !important;
}

/* 已改用自定义按钮，无需TDesign按钮样式覆盖 */
