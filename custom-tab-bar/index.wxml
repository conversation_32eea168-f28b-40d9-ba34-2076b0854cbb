<view class="wrapper">
	<t-tab-bar value="{{active}}" bindchange="onChange" split="{{false}}" theme="tag">
		<t-tab-bar-item wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index">
			<view
				class="custom-tab-bar-wrapper {{active === index ? 'active' : ''}} {{item.isAddButton ? 'add-button' : ''}}">
				<view wx:if="{{item.isAddButton}}" class="add-button-container">
					<image class="add-icon" src="{{item.iconPath}}" mode="aspectFit" />
				</view>
				<view wx:else class="tab-item">
					<image class="tab-icon" src="{{active === index ? item.selectedIconPath : item.iconPath}}" mode="aspectFit" />
					<view class="text">{{ item.text }}</view>
				</view>
			</view>
		</t-tab-bar-item>
	</t-tab-bar>
</view>

<!-- 扫码设备确认弹窗 -->
<t-popup visible="{{showScanConfirmPopup}}" placement="bottom" bind:visible-change="onScanPopupVisibleChange">
	<view class="scan-confirm-popup">
		<!-- 弹窗标题 -->
		<view class="popup-header">
			<text class="popup-title">请确认该设备</text>
		</view>

		<!-- 设备信息 -->
		<view class="device-info-section">
			<view class="device-info-item">
				<text class="device-info-label">电池序列号</text>
				<view class="device-info-divider"></view>
				<text class="device-info-value">{{scannedDeviceInfo.serialNumber}}</text>
			</view>

			<view class="device-info-item">
				<text class="device-info-label">固件版本</text>
				<view class="device-info-divider"></view>
				<text class="device-info-value">{{scannedDeviceInfo.firmwareVersion}}</text>
			</view>

			<view class="device-info-item">
				<text class="device-info-label">硬件版本</text>
				<view class="device-info-divider"></view>
				<text class="device-info-value">{{scannedDeviceInfo.hardwareVersion}}</text>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="popup-buttons">
			<view class="custom-cancel-btn" bind:tap="onCancelDevice">
				取消
			</view>
			<view class="custom-confirm-btn" bind:tap="onConfirmDevice">
				确认
			</view>
		</view>
	</view>
</t-popup>