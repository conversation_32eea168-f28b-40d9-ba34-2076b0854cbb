// app.ts
import { eventBus } from './utils/eventBus';
import { setENV } from './config/index';
import updateManager from './common/updateManager';
import authService from './common/auth';

// const isDevTool = wx.getSystemInfoSync().platform.toLowerCase() === 'devtools';
// if (!isDevTool) {
//   ['log', 'warn', 'error', 'info', 'debug'].forEach((method) => {
//     (console as any)[method] = function () {};
//   });
// }

App<IAppOption>({
  globalData: {
    isLoggedIn: false,
    userInfo: null,
  },

  onLaunch(options: WechatMiniprogram.App.LaunchShowOption) {
    console.log('小程序启动，开始初始化...');

    // 设置全局事件总线
    (this as any).globalData.eventBus = eventBus;

    // 设置环境
    setENV(options.query?.ENV);

    // 执行自动登录
    this.performAutoLogin();
  },

  onShow() {
    updateManager();

    // 每次显示时检查登录状态
    this.updateLoginStatus();
  },

  /**
   * 执行自动登录
   */
  async performAutoLogin() {
    try {
      // wx.showLoading({
      //   title: '初始化中...',
      //   mask: true,
      // });

      console.log('开始自动登录流程...');
      const loginSuccess = await authService.autoLogin();

      if (loginSuccess) {
        console.log('自动登录成功');
        this.globalData.isLoggedIn = true;
        this.globalData.userInfo = authService.getCurrentUser();

        // 触发登录成功事件
        eventBus.emit('loginSuccess', {
          userInfo: this.globalData.userInfo,
        });
      } else {
        console.log('自动登录失败或需要用户注册');
        this.globalData.isLoggedIn = false;
        this.globalData.userInfo = null;

        // 检查是否是需要注册的情况
        const sessionData = authService.getSessionData();
        if (sessionData && sessionData.openid) {
          console.log('检测到未注册用户，触发注册引导');
          // 触发需要注册事件
          eventBus.emit('needRegister', {
            sessionData: sessionData,
          });
        } else {
          // 触发登录失败事件
          eventBus.emit('loginFailed');
        }
      }
    } catch (error) {
      console.error('自动登录过程出错:', error);
      this.globalData.isLoggedIn = false;
      this.globalData.userInfo = null;

      // 触发登录错误事件
      eventBus.emit('loginError', { error });

      // 显示错误提示
      wx.showToast({
        title: '初始化失败，请重试',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 更新登录状态
   */
  updateLoginStatus() {
    const isLoggedIn = authService.isLoggedIn();
    const userInfo = authService.getCurrentUser();

    // 更新全局状态
    this.globalData.isLoggedIn = isLoggedIn;
    this.globalData.userInfo = userInfo;

    // 如果状态发生变化，触发相应事件
    if (isLoggedIn && userInfo) {
      eventBus.emit('loginStatusUpdate', {
        isLoggedIn: true,
        userInfo: userInfo,
      });
    } else {
      eventBus.emit('loginStatusUpdate', {
        isLoggedIn: false,
        userInfo: null,
      });
    }
  },

  /**
   * 手动登录（供页面调用）
   */
  async login(): Promise<boolean> {
    try {
      const loginSuccess = await authService.autoLogin();
      this.updateLoginStatus();
      return loginSuccess;
    } catch (error) {
      console.error('手动登录失败:', error);
      return false;
    }
  },

  /**
   * 登出（供页面调用）
   */
  logout() {
    authService.logout();
    this.globalData.isLoggedIn = false;
    this.globalData.userInfo = null;

    // 触发登出事件
    eventBus.emit('logout');

    console.log('用户已登出');
  },

  /**
   * 获取当前登录状态
   */
  getLoginStatus() {
    return {
      isLoggedIn: this.globalData.isLoggedIn,
      userInfo: this.globalData.userInfo,
    };
  },

  /**
   * 刷新用户信息
   */
  async refreshUserInfo(): Promise<boolean> {
    try {
      const success = await authService.refreshUserInfo();
      if (success) {
        this.globalData.userInfo = authService.getCurrentUser();
        eventBus.emit('userInfoUpdate', {
          userInfo: this.globalData.userInfo,
        });
      }
      return success;
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      return false;
    }
  },
});
