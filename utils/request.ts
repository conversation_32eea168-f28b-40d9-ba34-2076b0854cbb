/**
 * 导入其他模块
 */
import Http from '../common/http';
import storage from '../common/storage';
import config from '../config/index';

import qs from '../common/qs';

const codeMessage: Record<number, string> = {
  200: '服务器成功返回请求的数据',
  201: '新建或修改数据成功',
  202: '一个请求已经进入后台排队（异步任务）',
  204: '删除数据成功',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作',
  401: '用户没有权限（令牌、用户名、密码错误）',
  403: '用户得到授权，但是访问是被禁止的',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作',
  406: '请求的格式不可得',
  410: '请求的资源被永久删除，且不会再得到的',
  422: '当创建一个对象时，发生一个验证错误',
  500: '服务器发生错误，请检查服务器',
  502: '网关错误',
  503: '服务不可用，服务器暂时过载或维护',
  504: '网关超时',
};

/**
 * result api 请求封装
 * 对响应的数据做一次处理
 */
class Restful {
  constructor() {}

  /**
   * 创建http请求客户端对象
   * 并且设置token头信息
   */
  createHttpClient(accessToken: any, isForm?: boolean): Http {
    if (accessToken && accessToken.token) {
      let header = {};
      let token = storage.getToken() || accessToken.token;
      header = {
        Authorization: `${'Bearer'} ${token}`,
      };
      let http = new Http(isForm);
      return http.setHeader(header);
    } else {
      return new Http(isForm).setHeader(null);
    }
  }

  /**
   * get
   */
  get(url: string, params?: any): Promise<any> {
    return this.ensureAccessToken().then((accessToken) => {
      return this.createHttpClient(accessToken, false)
        .doGet(this.format(url, params))
        .then(this.checkStatus)
        .then(this.thenData);
    });
  }

  /**
   * post
   */
  post(url, data) {
    return this.ensureAccessToken().then((accessToken) => {
      return this.createHttpClient(accessToken, false)
        .doPost(this.format(url), data)
        .then(this.checkStatus)
        .then(this.thenResult);
    });
  }

  uploadFile(url, params) {
    const { filePath, name, formData } = params;
    return this.ensureAccessToken().then((accessToken) => {
      return this.createHttpClient(accessToken, false)
        .doUploadFile(this.format(url), filePath, name, formData)
        .then(this.checkStatus)
        .then(this.thenResult);
    });
  }

  /**
   * post form
   */
  postForm(url, data) {
    return this.ensureAccessToken().then((accessToken) => {
      return this.createHttpClient(accessToken, true)
        .doPost(this.format(url), data)
        .then(this.checkStatus)
        .then(this.thenResult);
    });
  }

  /**
   * delete
   */
  delete(url, params) {
    return this.ensureAccessToken().then((accessToken) => {
      return this.createHttpClient(accessToken, false)
        .doDelete(this.format(url, params))
        .then(this.checkStatus)
        .then(this.thenResult);
    });
  }

  /**
   * put
   */
  put(url, data) {
    return this.ensureAccessToken().then((accessToken) => {
      return this.createHttpClient(accessToken, false)
        .doPut(this.format(url), data)
        .then(this.checkStatus)
        .then(this.thenResult);
    });
  }

  /**
   * patch
   */
  patch(url, data) {
    return this.ensureAccessToken().then((accessToken) => {
      return this.createHttpClient(accessToken, false)
        .doPatch(this.format(url), data)
        .then(this.checkStatus)
        .then(this.thenResult);
    });
  }

  /**
   * get for no token
   */
  getData(url, params) {
    return new Http()
      .doGet(this.format(url, params))
      .then(this.checkStatus)
      .then(this.thenResult);
  }

  /**
   * post for no token
   */
  postFormData(url, data) {
    return new Http(true)
      .doPost(this.format(url), data)
      .then(this.checkStatus)
      .then(this.thenResult);
  }

  /**
   * 格式化url
   * 加上主机地址
   */
  format(url, params) {
    const pdata =
      params != null && params != undefined && Object.keys(params).length > 0
        ? `?${qs.stringify(params, { arrayFormat: 'repeat' })}`
        : '';
    if (params) {
      return config.host + url + pdata;
    } else {
      return config.host + url;
    }
  }

  /**
   * 检查响应状态是否正常
   */
  checkStatus(response) {
    if (wx.showLoading) {
      wx.hideLoading();
    }
    // 正常
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return response;
    }
    if (response.statusCode === 400 || response.statusCode === 403) {
      const { fieldErrors, message, error } = response.data;
      if (fieldErrors) {
        const keys = Object.keys(fieldErrors);
        if (keys && keys.length > 0) {
          // 只显示第一个错误信息，如果表单有多个域，那就在blur的时候校验一次
          setTimeout(() => {
            wx.showToast({ title: fieldErrors[keys[0]], icon: 'none' });
          }, 2000);
        }
      } else if (message) {
        setTimeout(() => {
          wx.showToast({ title: message, icon: 'none', duration: 2000 });
        }, 2000);
      }
      console.warn('请求参数错误:', error);
      if (error) {
        setTimeout(() => {
          wx.showToast({
            title: error.validationErrors[0].message,
            icon: 'none',
            duration: 2000,
          });
        }, 200);
        throw new Error(response);
      }
      return response;
    }

    // 500 的错误res对象继续返回调用者，否则抛出异常
    if (response.statusCode === 500) {
      const { error } = response.data;
      // setTimeout(() => {
      //   wx.showToast({
      //     title:
      //       error && error.message
      //         ? `${error.message}`
      //         : '服务请求错误，请稍后重试',
      //     icon: 'none',
      //   });
      // }, 1000);
      console.error('服务请求错误:', response);
      throw new Error(response.message || '');
    }

    if (response.statusCode === 502) {
      wx.showToast({ title: '服务维护中，请稍后重试', icon: 'none' });
      console.error('服务维护中，请稍后重试:', response);
      throw new Error(response.message || '服务维护中，请稍后重试');
    }

    // 错误
    const errortext = codeMessage[response.statusCode] || response.statusText;
    if (response.statusCode === 401) {
      storage.logout();
      wx.navigateTo({
        url: '/pages/login/login',
      });
      // wx.showToast({ title: "请先授权登录", icon: "none" });
    } else {
      console.log('请求错误:', response);
      const { error } = response;
      setTimeout(() => {
        wx.showToast({
          title: errortext || (error && error.message ? error.message : ''),
          icon: 'none',
        });
      }, 2000);
    }
    const error = new Error(errortext);
    error.name = response.statusCode;
    error.response = response;
    console.error('请求错误:' + response.statusCode, error);
    throw error;
  }

  /**
   * 获取响应数据
   * 主要针对的是get请求的结果
   */
  thenData(res) {
    if (res && res.data && res.data.data) {
      return res.data.data;
    }
    if (res && typeof res.data == 'boolean') {
      return res;
    }
    if (res && res.data) {
      return res.data;
    }
    return res;
  }

  /**
   * 获取响应数据
   * 主要是post、delete、put的操作结果
   */
  thenResult(res) {
    if (res) {
      return res.data;
    }
    return res;
  }

  /**
   * 初始化access token
   */
  ensureAccessToken() {
    // 调用用户传入的获取token的异步方法，获得token之后使用（并缓存它）。
    const accessToken = storage.getAccessToken();
    if (!accessToken) {
      // 如果没有token，说明还没登录，不需要刷新token
      return Promise.resolve(null);
    }
    if (accessToken && accessToken.isValid()) {
      return Promise.resolve(accessToken);
    }
    return this.freshAccessToken(accessToken);
  }

  /**
   * 刷新access token
   * 不判断refreash token是否过期，如果过期刷新会失败，返回401
   * 因为这里有一点点小问题，store 获取为 undefined
   */
  freshAccessToken(accessToken) {
    return new Promise((resolve, reject) => {
      const url = this.format('/api/mini/login/refresh');
      const freshToken = storage.getRefreshToken();
      if (!freshToken) {
        // 不需要刷新，直接返回
        return accessToken;
      }
      const params = {
        // access_token: accessToken.token,
        refreshToken: freshToken.token,
      };
      new Http().doPost(url, params).then(
        (res) => {
          if (res.statusCode == 200) {
            const data = res.data;
            storage.saveRefreshToken(data.refresh_token);
            const newAccessToken = storage.saveAccessToken(
              data.access_token,
              data.expires_in
            );
            resolve(newAccessToken);
          } else {
            storage.logout();
            // wx.showToast({ title: "Token已过期，请重新授权", icon: "none" });
            console.error('Token已过期，请重新授权');
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/tabbar/index/index',
              });
            }, 3000);
          }
        },
        (err) => {
          // wx.showToast({ title: "刷新Token错误，请稍后再试", icon: "none" });
          console.error('刷新Token错误，请稍后再试', err);
          reject(err);
        }
      );
    });
  }
}

function request(params) {
  switch (params.method) {
    case 'GET':
      return new Restful().get(params.url, params.params);
    case 'POST':
      return new Restful().post(params.url, params.data);
    case 'POSTFORM':
      return new Restful().postForm(params.url, params.data);
    case 'DELETE':
      return new Restful().delete(params.url, params.params);
    case 'PUT':
      return new Restful().put(params.url, params.data);
    case 'PATCH':
      return new Restful().patch(params.url, params.data);
    case 'UPLOAD':
      return new Restful().uploadFile(params.url, params.params);
    default:
      return new Restful().get(params.url, params.params);
  }
}
// 导出Restful类
export { Restful };

// 新建一个restful对象
export default request;

/**
 * 导出新的对象，别名。目的是为了可以使用get，delete，避免跟关键字冲突
 * 注意：this 指针问题，加上 () => {} 表达式解决
 * 调用方式：
 * let restful = require('restful.js');
 * restful.get('http://domain.com/api/getuser/1');
 * restful.post('http://domain.com/api/getuser/1', data);
 * restful.delete('http://domain.com/api/getuser/1');
 * restful.put('http://domain.com/api/getuser/1', data);
 */
// export default
// {
//   "get": (url, params) => restful.doGet(url, params),
//   "post": (url, data) => restful.doPost(url, data),
//   "postForm": (url, data) => restful.doPostForm(url, data),
//   "delete": (url, params) => restful.doDelete(url, params),
//   "put": (url, data) => restful.doPut(url, data),
//   "postFormData": (url, data) => restful.doPostFormData(url, data),
//   "getData": (url, params) => restful.doGetData(url, params),
// };
