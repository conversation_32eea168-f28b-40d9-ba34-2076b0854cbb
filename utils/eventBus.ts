// utils/eventBus.ts
type EventCallback = (data?: any) => void;

class EventBus {
  private events: Record<string, EventCallback[]>;

  constructor() {
    this.events = {}; // 存储事件
  }

  /**
   * 订阅事件
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  on(event: string, callback: EventCallback): void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  /**
   * 取消订阅事件
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  off(event: string, callback: EventCallback): void {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter((cb) => cb !== callback);
    }
  }

  /**
   * 发布事件
   * @param {string} event 事件名称
   * @param {any} data 传递的数据
   */
  emit(event: string, data?: any): void {
    if (this.events[event]) {
      this.events[event].forEach((cb) => cb(data));
    }
  }

  /**
   * 一次性订阅事件
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  once(event: string, callback: EventCallback): void {
    const onceCallback = (data?: any) => {
      callback(data);
      this.off(event, onceCallback);
    };
    this.on(event, onceCallback);
  }
}

// 导出单例实例
export const eventBus = new EventBus();
