/**
 * 自定义TabBar工具函数
 */

/**
 * 更新TabBar状态
 * @param {Object} page - 当前页面实例
 */
export function updateTabBarState(page) {
  if (!page) {
    console.warn('updateTabBarState: page参数为空');
    return;
  }

  // 获取自定义tabbar实例
  const tabbar = page.getTabBar && page.getTabBar();
  if (!tabbar) {
    console.warn('updateTabBarState: 无法获取tabbar实例');
    return;
  }

  console.log('updateTabBarState: 开始更新tabbar状态');

  // 初始化tabbar状态
  tabbar.init();

  // 如果需要，可以调用强制刷新
  if (tabbar.forceRefresh) {
    tabbar.forceRefresh();
  }
}

/**
 * 在页面onShow中调用此方法
 * @param {Object} page - 当前页面实例
 */
export function onPageShow(page) {
  console.log('onPageShow: 页面显示，准备更新tabbar');

  // 延迟一帧执行，确保页面渲染完成
  wx.nextTick(() => {
    updateTabBarState(page);
  });

  // 额外的延迟执行，处理一些边缘情况
  setTimeout(() => {
    updateTabBarState(page);
  }, 150);
}

/**
 * 防抖函数，防止频繁切换
 */
let debounceTimer = null;
export function debounceUpdateTabBar(page, delay = 100) {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }

  debounceTimer = setTimeout(() => {
    updateTabBarState(page);
    debounceTimer = null;
  }, delay);
}

/**
 * 强制更新TabBar状态，用于解决状态不同步的问题
 * @param {Object} page - 当前页面实例
 */
export function forceUpdateTabBar(page) {
  if (!page) return;

  console.log('forceUpdateTabBar: 强制更新tabbar状态');

  const tabbar = page.getTabBar && page.getTabBar();
  if (tabbar) {
    // 多次尝试更新，确保状态正确
    tabbar.init();

    setTimeout(() => {
      tabbar.init();
    }, 50);

    setTimeout(() => {
      tabbar.init();
    }, 100);
  }
}
