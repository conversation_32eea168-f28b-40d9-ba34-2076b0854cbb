import { Restful } from '../utils/request';
import type {
  BaseResponse,
  GetCaptchaParams,
  MobileCaptchaVerifyRequest,
} from './types';

const request = new Restful();

/**
 * 获取手机号验证码
 * @param params 包含手机号的参数
 * @returns Promise<string> 返回验证码key
 */
export function getCaptchaByMobile(
  params: GetCaptchaParams
): Promise<BaseResponse<string>> {
  return request.get('/v1/captcha/mobile', params);
}

/**
 * 验证手机号验证码
 * @param data 验证码验证请求数据
 * @returns Promise<BaseResponse>
 */
export function verifyCaptcha(
  data: MobileCaptchaVerifyRequest
): Promise<BaseResponse> {
  return request.post('/v1/captcha/mobile/verify', data);
}

export default {
  getCaptchaByMobile,
  verifyCaptcha,
};
