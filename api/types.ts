// 基础响应类型
export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface ErrorResponse {
  code: number;
  message: string;
  detail: string;
}

// 认证相关类型
export interface Code2SessionResponse {
  openid: string;
  unionid: string;
}

export interface AccessToken {
  accessToken: string;
  expireAt: number;
}

// 手机号相关类型
export type Mobile = string; // 11位手机号，格式：^1[3-9]\d{9}$

export type CaptchaCode = string; // 4位数字验证码，格式：^[0-9]{4}$

export interface MobileCaptchaVerifyRequest {
  key: string; // 验证码key，长度为20
  code: CaptchaCode;
}

// 用户相关类型
export interface WxmpUser {
  id: string;
  wx_openid: string;
  wx_unionid: string;
  nickname: string;
  avatar: string;
  mobile: string;
  user_id?: string;
  edges?: {
    user?: User;
    devices?: Device[];
  };
}

export interface User {
  id: string;
  username: string;
  mobile: string;
  avatar?: string;
  blocked: boolean;
  createdAt: number;
  updatedAt: number;
  organizationId: string;
  edges?: {
    organization?: Organization;
    roles?: Role[];
  };
}

export interface CreateUserRequest {
  wx_openid: string;
  wx_unionid: string;
  nickname: string;
  avatar: string;
  mobile: string;
}

// 设备相关类型
export interface Device {
  id: string;
  sn: string;
  name?: string;
  properties: Record<string, any>;
  online: boolean;
  uploadTime: number;
  activeTime: number;
  createdAt: number;
  updatedAt: number;
  description?: string;
  productId: string;
  edges?: {
    product?: Product;
    users?: User[];
    groups?: Group[];
    wxmp_user?: WxmpUser[];
  };
}

export interface BindDeviceRequest {
  sn: string; // 设备序列号，长度16-20位
  real_name: string; // 真实姓名，长度2-5位
  car_type: string; // 车辆类型，长度2-5位
  car_no: string; // 车牌号，长度5-10位
  mobile: Mobile; // 手机号，11位
}

// 组织相关类型
export interface Organization {
  id: string;
  name: string;
  logo: string;
  createdAt: number;
  updatedAt: number;
  edges?: {
    users?: User[];
  };
}

export interface Role {
  id: string;
  name: string;
  description: string;
  createdAt: number;
  updatedAt: number;
  edges?: {
    organization?: Organization;
    permissions?: Permission[];
    users?: User[];
  };
}

export interface Permission {
  id: string;
  name: string;
  key: string;
  description: string;
  createdAt: number;
  updatedAt: number;
  edges?: {
    roles?: Role[];
  };
}

export interface Product {
  id: string;
  name: string;
  description: string;
  productKey: number;
  createdAt: number;
  updatedAt: number;
  edges?: {
    organizations?: Organization[];
  };
}

export interface Group {
  id: string;
  name: string;
  description?: string;
  createdAt: number;
  updatedAt: number;
  parentId?: string;
  organizationId: string;
  edges?: {
    organization?: Organization;
    users?: User[];
    devices?: Device[];
  };
}

// 查询参数类型
export interface GetUserParams {
  openid?: string;
  unionid?: string;
  code?: string;
}

export interface LoginParams {
  code: string;
}

export interface Code2SessionParams {
  code: string;
}

export interface GetPhoneNumberParams {
  code: string;
}

export interface GetCaptchaParams {
  mobile: Mobile;
}

// 对象存储相关类型
export interface UploadObjectParams {
  key?: string; // 文件路径，支持目录结构，例如：test/test.txt （可选）
  filePath: string; // 本地文件路径
}

export interface GetObjectParams {
  key?: string; // 文件路径（可选）
}

// 手机号获取结果类型
export interface GetPhoneNumberResult {
  phoneNumber: string;
  purePhoneNumber: string;
  countryCode: string;
  watermark: {
    timestamp: number;
    appid: string;
  };
}

// 用户信息更新请求类型
export interface UpdateUserRequest {
  nickname?: string;
  avatar?: string;
  mobile?: string;
}
