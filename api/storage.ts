import { Restful } from '../utils/request';
import config from '../config/index';
import type {
  BaseResponse,
  UploadObjectParams,
  GetObjectParams,
} from './types';

const request = new Restful();

/**
 * 上传对象到存储
 * @param key 文件路径，支持目录结构，例如：test/test.txt
 * @param filePath 本地文件路径
 * @returns Promise<BaseResponse>
 */
export function uploadObject(
  key: string,
  filePath: string
): Promise<BaseResponse> {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${config.host}/v1/object`,
      filePath,
      name: 'file',
      formData: {
        key: key,
      },
      header: {
        Authorization: `Bearer ${wx.getStorageSync('access_token')}`,
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (e) {
            resolve({ code: 0, message: 'success', data: res.data });
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
        }
      },
      fail: reject,
    });
  });
}

/**
 * 获取存储的对象
 * @param key 文件路径（可选）
 * @returns Promise<ArrayBuffer> 返回文件的二进制数据
 */
export function getObject(key?: string): Promise<ArrayBuffer> {
  // 注意：这个接口返回的是二进制数据，不是标准的 JSON 响应格式
  return new Promise((resolve, reject) => {
    const queryString = key ? `?key=${encodeURIComponent(key)}` : '';

    wx.request({
      url: `${config.host}/v1/object${queryString}`,
      method: 'GET',
      responseType: 'arraybuffer',
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data as ArrayBuffer);
        } else {
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      },
      fail: reject,
    });
  });
}

/**
 * 上传图片文件
 * @param key 文件路径
 * @param filePath 本地文件路径
 * @returns Promise<BaseResponse>
 */
export function uploadImage(
  key: string,
  filePath: string
): Promise<BaseResponse> {
  return uploadObject(key, filePath);
}

/**
 * 上传文件（通用方法）
 * @param filePath 本地文件路径
 * @param key 文件路径（可选）
 * @returns Promise<BaseResponse>
 */
export function uploadFile(
  filePath: string,
  key?: string
): Promise<BaseResponse> {
  return new Promise((resolve, reject) => {
    const formData: Record<string, any> = {};
    if (key) {
      formData.key = key;
    }

    wx.uploadFile({
      url: `${config.host}/v1/object`,
      filePath,
      name: 'file',
      formData,
      header: {
        Authorization: `Bearer ${wx.getStorageSync('access_token')}`,
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (e) {
            resolve({ code: 0, message: 'success', data: res.data });
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
        }
      },
      fail: reject,
    });
  });
}

export default {
  uploadObject,
  getObject,
  uploadImage,
  uploadFile,
};
