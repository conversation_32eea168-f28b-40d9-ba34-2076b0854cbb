// 导出所有类型定义
export * from './types';

// 导出API模块
export { default as authApi } from './auth';
export { default as userApi } from './user';
export { default as deviceApi } from './device';
export { default as captchaApi } from './captcha';
export { default as storageApi } from './storage';

// 导出单个API函数，方便直接使用
export { code2Session, login, loginByMobile } from './auth';

export {
  getMe,
  updateMe,
  getUser,
  createUser,
  getUserDevices,
  bindDevice,
  getUserPhoneNumber,
} from './user';

export { getDeviceBySn } from './device';

export { getCaptchaByMobile, verifyCaptcha } from './captcha';

export { uploadObject, getObject, uploadImage, uploadFile } from './storage';
