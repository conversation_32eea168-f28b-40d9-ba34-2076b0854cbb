import { Restful } from '../utils/request';
import type {
  BaseResponse,
  WxmpUser,
  CreateUserRequest,
  GetUserParams,
  Device,
  UpdateUserRequest,
  GetPhoneNumberParams,
  GetPhoneNumberResult,
  BindDeviceRequest,
} from './types';

const request = new Restful();

/**
 * 获取当前用户信息
 * @returns Promise<WxmpUser>
 */
export function getMe(): Promise<BaseResponse<WxmpUser>> {
  return request.get('/v1/me');
}

/**
 * 更新当前用户信息
 * @param data 用户更新数据
 * @returns Promise<BaseResponse>
 */
export function updateMe(data: UpdateUserRequest): Promise<BaseResponse> {
  return request.patch('/v1/me', data);
}

/**
 * 获取用户信息
 * @param params 查询参数，可以包含openid、unionid或code
 * @returns Promise<WxmpUser>
 */
export function getUser(
  params?: GetUserParams
): Promise<BaseResponse<WxmpUser>> {
  return request.get('/v1/user', params);
}

/**
 * 创建用户
 * @param data 用户创建数据
 * @returns Promise<WxmpUser>
 */
export function createUser(
  data: CreateUserRequest
): Promise<BaseResponse<WxmpUser>> {
  return request.post('/v1/user', data);
}

/**
 * 获取用户所有设备
 * @returns Promise<Device[]>
 */
export function getUserDevices(): Promise<BaseResponse<Device[]>> {
  return request.get('/v1/me/devices');
}

/**
 * 绑定设备到当前用户
 * @param data 设备绑定请求数据
 * @returns Promise<BaseResponse>
 */
export function bindDevice(data: BindDeviceRequest): Promise<BaseResponse> {
  return request.post('/v1/me/devices', data);
}

/**
 * 获取微信用户手机号
 * @param params 包含手机号获取凭证的参数
 * @returns Promise<GetPhoneNumberResult>
 */
export function getUserPhoneNumber(
  params: GetPhoneNumberParams
): Promise<BaseResponse<GetPhoneNumberResult>> {
  return request.get('/v1/getuserphonenumber', params);
}

export default {
  getMe,
  updateMe,
  getUser,
  createUser,
  getUserDevices,
  bindDevice,
  getUserPhoneNumber,
};
