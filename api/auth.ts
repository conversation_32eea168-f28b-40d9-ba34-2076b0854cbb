import { Restful } from '../utils/request';
import type {
  BaseResponse,
  Code2SessionResponse,
  Code2SessionParams,
  AccessToken,
  LoginParams,
  MobileCaptchaVerifyRequest,
} from './types';

const request = new Restful();

/**
 * 小程序登录
 * @param params 包含微信小程序登录code
 * @returns Promise<Code2SessionResponse>
 */
export function code2Session(
  params: Code2SessionParams
): Promise<BaseResponse<Code2SessionResponse>> {
  return request.getData('/v1/code2session', params);
}

/**
 * 用户登录
 * @param params 包含微信小程序登录code
 * @returns Promise<AccessToken | Code2SessionResponse>
 */
export function login(
  params: LoginParams
): Promise<BaseResponse<AccessToken | Code2SessionResponse>> {
  return request.getData('/v1/login', params);
}

/**
 * 手机号登录
 * @param data 手机号验证码验证请求
 * @returns Promise<AccessToken>
 */
export function loginByMobile(
  data: MobileCaptchaVerifyRequest
): Promise<BaseResponse<AccessToken>> {
  return request.post('/v1/login/mobile', data);
}

export default {
  code2Session,
  login,
  loginByMobile,
};
